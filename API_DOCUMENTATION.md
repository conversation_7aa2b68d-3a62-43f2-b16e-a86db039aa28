# 勤工助学报酬申报新增接口文档

## 概述

本次新增了两个接口，用于支持前端根据岗位查询用工学生名单，并批量设置工时后发起报酬申报。

## 接口列表

### 1. 查询某个岗位下的用工学生名单

**接口地址：** `GET /api/workstudy/qgzx-remuneration-apply/job-students/{jobId}`

**权限标识：** `workstudy:qgzxRemunerationApply:list`

**请求参数：**
- `jobId` (路径参数): 岗位ID

**响应数据：**
```json
[
  {
    "studentApplyId": "学生申请ID",
    "xgh": "学号",
    "xm": "学生姓名",
    "xymc": "学院名称",
    "zymc": "专业名称",
    "bjmc": "班级名称",
    "lxdh": "联系电话",
    "ygzt": "用工状态代码",
    "ygztName": "用工状态名称",
    "sqsj": "申请时间",
    "hourlyRate": "时薪",
    "canSetWorkHours": "是否可以设置工时",
    "remark": "备注"
  }
]
```

**使用说明：**
- 返回指定岗位下所有学生申请记录
- `canSetWorkHours` 字段用于前端判断该学生是否可以设置工时（只有用工状态为"YG"的学生才能设置工时）
- 按申请时间倒序排列

### 2. 批量设置工时并发起报酬申报

**接口地址：** `POST /api/workstudy/qgzx-remuneration-apply/applyByEmployerWithBatchWorkHours`

**权限标识：** `workstudy:qgzxRemunerationApply:apply`

**请求体：**
```json
{
  "jobId": "岗位ID",
  "sbny": "申报年月(格式：yyyy-MM)",
  "sbsm": "申报说明",
  "studentWorkHours": [
    {
      "studentApplyId": "学生申请ID",
      "xgh": "学号",
      "workHours": "工时数",
      "hourlyRate": "时薪",
      "remunerationAmount": "报酬金额(可选，不传则自动计算)",
      "remark": "备注"
    }
  ]
}
```

**响应：** 无返回数据（成功时返回200状态码）

**使用说明：**
- 支持批量为多个学生设置工时
- 会自动验证岗位是否存在、学生申请是否有效等
- 如果不传 `remunerationAmount`，系统会自动根据工时和时薪计算
- 根据岗位的 `bcsfsh` 字段判断是否需要审核：
  - `bcsfsh` 为"是"：提交审核流程
  - `bcsfsh` 为"否"：直接通过，无需审核

## 前端使用流程

1. **查询学生名单**
   ```javascript
   // 获取指定岗位下的学生名单
   const students = await getJobStudentList(jobId);
   
   // 过滤出可以设置工时的学生
   const availableStudents = students.filter(student => student.canSetWorkHours);
   ```

2. **批量设置工时并申报**
   ```javascript
   const batchData = {
     jobId: "岗位ID",
     sbny: "2025-01", // 申报年月
     sbsm: "2025年1月报酬申报",
     studentWorkHours: availableStudents.map(student => ({
       studentApplyId: student.studentApplyId,
       xgh: student.xgh,
       workHours: 40, // 前端设置的工时
       hourlyRate: student.hourlyRate,
       remark: "用人单位设置工时"
     }))
   };
   
   await applyByEmployerWithBatchWorkHours(batchData);
   ```

## 错误处理

接口会返回以下错误信息：
- "岗位不存在" - 当岗位ID无效时
- "学生申请不存在" - 当学生申请ID无效时
- "该岗位在{申报年月}已有报酬申报记录，请勿重复提交" - 当同一岗位同一月份已有申报记录时
- "学生工时必须大于0" - 当工时数据无效时
- "时薪必须大于0" - 当时薪数据无效时

## 注意事项

1. 确保用户具有相应的权限标识
2. 申报年月格式必须为 `yyyy-MM`
3. 只有用工状态为"YG"的学生才能设置工时
4. 同一岗位同一月份不能重复申报
5. 所有数值字段（工时、时薪、报酬金额）必须大于0
