package com.sanythadmin.project.workstudy.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 批量设置工时DTO
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
public class QgzxBatchWorkHoursDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 岗位ID
     */
    private String jobId;

    /**
     * 申报年月（格式：yyyy-MM）
     */
    private String sbny;

    /**
     * 申报说明
     */
    private String sbsm;

    /**
     * 学生工时明细列表
     */
    private List<StudentWorkHoursDetail> studentWorkHours;

    /**
     * 学生工时明细
     */
    @Data
    public static class StudentWorkHoursDetail implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 学生申请ID
         */
        private String studentApplyId;

        /**
         * 学号
         */
        private String xgh;

        /**
         * 工时
         */
        private BigDecimal workHours;

        /**
         * 时薪
         */
        private BigDecimal hourlyRate;

        /**
         * 报酬金额
         */
        private BigDecimal remunerationAmount;

        /**
         * 备注
         */
        private String remark;
    }
}
