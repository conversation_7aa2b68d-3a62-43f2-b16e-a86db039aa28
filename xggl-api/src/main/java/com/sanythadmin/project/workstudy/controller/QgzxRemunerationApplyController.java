package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.FormDataWrapper;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workflow.service.WorkflowNodeFormService;
import com.sanythadmin.project.workflow.service.WorkflowNodeService;
import com.sanythadmin.project.workflow.service.WorkflowService;
import com.sanythadmin.project.workstudy.dto.QgzxApprovalMessage;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationApply;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationApplyParam;
import com.sanythadmin.project.workstudy.service.QgzxRemunerationApplyService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

/**
 * 勤工助学/报酬申报控制器
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-remuneration-apply")
@RequiredArgsConstructor
public class QgzxRemunerationApplyController extends BaseController {

    private final QgzxRemunerationApplyService qgzxRemunerationApplyService;
    private final WorkflowService workflowService;
    private final WorkflowNodeService workflowNodeService;
    private final SysModuleSetupService sysModuleSetupService;
    private final WorkflowNodeFormService workflowNodeFormService;

    /**
     * 审核人分页查询报酬申报（权限标识：workstudy:qgzxRemunerationApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:list')")
    @GetMapping("/page")
    public PageResult<QgzxRemunerationApply> page(QgzxRemunerationApplyParam param,
                                                   UserInfoParam userInfoParam,
                                                   WorkflowApprovalNodeParam approvalNodeParam) {
        return qgzxRemunerationApplyService.pageApprovalList(param, userInfoParam, approvalNodeParam);
    }

    /**
     * 审核人查询报酬申报（权限标识：workstudy:qgzxRemunerationApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:list')")
    @GetMapping()
    public List<QgzxRemunerationApply> list(QgzxRemunerationApplyParam param,
                                             UserInfoParam userInfoParam,
                                             WorkflowApprovalNodeParam approvalNodeParam) {
        return qgzxRemunerationApplyService.listApprovalList(param, userInfoParam, approvalNodeParam);
    }

    /**
     * 根据id查询报酬申报（权限标识：workstudy:qgzxRemunerationApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:list')")
    @GetMapping("/{id}")
    public QgzxRemunerationApply get(@PathVariable("id") String id) {
        return qgzxRemunerationApplyService.getById(id);
    }

    /**
     * 单个审核操作（权限标识：workstudy:qgzxRemunerationApply:operation）
     *
     * @param request 请求体
     */
    @OperationLog(module = "报酬申报审核", comments = "单个审核报酬申报")
    @PreAuthorize("hasAuthority('evaluate:approval:operation')")
    @PostMapping("/operation")
    public String operation(HttpServletRequest request) throws ExecutionException, InterruptedException {
        FormDataWrapper<WorkflowApprovalNodeRecord> wrapper = new FormDataWrapper<>(request, WorkflowApprovalNodeRecord.class);
        Map<String, List<MultipartFile>> multipartFileMap = wrapper.getFileFields();
        WorkflowApprovalNodeRecord record = wrapper.getObject();
        record.setId(null);
        record.setFileMap(multipartFileMap);
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        return qgzxRemunerationApplyService.approve(record, null, executor).get();
    }

    /**
     * 批量审核操作（权限标识：workstudy:qgzxRemunerationApply:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:operation')")
    @OperationLog(module = "报酬申报审核", comments = "批量审核报酬申报")
    @PostMapping("/batchOperation")
    public void batchOperation(HttpServletRequest request, WorkflowApprovalNodeRecord record, QgzxRemunerationApplyParam param, UserInfoParam userInfoParam,
                          WorkflowApprovalNodeParam approvalNodeParam) throws Exception {
        List<CompletableFuture<String>> futures = new ArrayList<>();
        List<QgzxApprovalMessage> errors = new ArrayList<>();
        Map<String, List<MultipartFile>> fileMap = CommonUtil.getFileFromRequest(request);
        param.setPage(1L);
        param.setLimit((long) (Integer.MAX_VALUE - 1));
        param.setInternalCall();
        UserInfoParam userInfoParam1 = new UserInfoParam();
        //TODO 有需要userInfoParam参数的话再单个赋值
        WorkflowApprovalNodeParam workflowApprovalNodeParam = new WorkflowApprovalNodeParam();
        //TODO approvalNodeParam同上
        PageResult<QgzxRemunerationApply> pageResult = qgzxRemunerationApplyService.pageApprovalList(param, userInfoParam1, workflowApprovalNodeParam);
        List<QgzxRemunerationApply> list = pageResult.getList();
        record.setId(null);
        record.setFileMap(fileMap);
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        for (QgzxRemunerationApply remunerationApply : list) {
            WorkflowApprovalNodeRecord temp = new WorkflowApprovalNodeRecord();
            BeanUtils.copyProperties(record, temp);
            temp.setApplicationId(remunerationApply.getId());
            CompletableFuture<String> future = qgzxRemunerationApplyService.approve(temp, remunerationApply, executor)
                    .handle((result, ex) -> {
                        if (ex != null)
                            errors.add(new QgzxApprovalMessage(remunerationApply.getXgh(), "",
                                    remunerationApply.getJobApplication().getJobName(), ex.getCause().getMessage()));
                        return null;
                    });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).join();
        if (!CollectionUtils.isEmpty(errors))
            EasyExcelHelper.writeApproveErrorInfo(errors, new String[]{"工号", "姓名", "申请报酬申报的岗位名称", "失败原因"},
                    new String[]{"xgh", "xm", "content", "message"});
    }

    /**
     * 审核人批量删除报酬申报（权限标识：workstudy:qgzxRemunerationApply:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:remove')")
    @OperationLog(module = "报酬申报审核", comments = "批量删除报酬申报")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        qgzxRemunerationApplyService.delete(ids.toArray(new String[]{}));
    }

    /**
     * 基于考勤记录自动生成并保存报酬申报（权限标识：workstudy:qgzxRemunerationApply:apply）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:apply')")
    @OperationLog(module = "报酬申报", comments = "自动生成并保存报酬申报")
    @PostMapping("/generateAndSave")
    public QgzxRemunerationApply generateAndSaveFromAttendance(@RequestParam String jobId, @RequestParam String sbny) {
        return qgzxRemunerationApplyService.generateAndSaveFromAttendance(jobId, sbny);
    }

    /**
     * 用人单位发起报酬申报
     * 根据岗位的bcsfsh字段判断：
     */
    @OperationLog(module = "报酬申报", comments = "用人单位发起报酬申报")
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:apply')")
    @PostMapping("/applyByEmployer")
    public void applyByEmployer(@RequestBody QgzxRemunerationApply remunerationApply) {
        qgzxRemunerationApplyService.applyByEmployer(remunerationApply);
    }


    /**
     * 用人单位查询本单位的报酬申报
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:list')")
    @GetMapping("/pageByEmployer")
    public PageResult<QgzxRemunerationApply> pageByEmployer(QgzxRemunerationApplyParam param) {
        return qgzxRemunerationApplyService.pageByEmployer(param);
    }

    /**
     * 根据岗位ID和申报年月查询申报记录（权限标识：workstudy:qgzxRemunerationApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:list')")
    @GetMapping("/by-job-and-month")
    public QgzxRemunerationApply getByJobIdAndSbny(@RequestParam String jobId, @RequestParam String sbny) {
        return qgzxRemunerationApplyService.getByJobIdAndSbny(jobId, sbny);
    }

    /**
     * 添加报酬,直接添加无需审核（权限标识：workstudy:qgzxRemunerationApply:addByCenter）
     */
    @OperationLog(module = "报酬申报", comments = "添加报酬")
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:addByCenter')")
    @PostMapping("/addRemuneration")
    public void addRemuneration(@RequestBody QgzxRemunerationApply remunerationApply) {
        qgzxRemunerationApplyService.addRemuneration(remunerationApply);
    }

    /**
     * 删除报酬，可删除通过的，不受限制（权限标识：workstudy:qgzxRemunerationApply:removeByCenter）
     */
    @OperationLog(module = "报酬申报", comments = "删除报酬")
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationApply:removeByCenter')")
    @PostMapping("/removeRemuneration")
    public void removeRemuneration(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        qgzxRemunerationApplyService.removeRemuneration(ids.toArray(new String[]{}));
    }
}
