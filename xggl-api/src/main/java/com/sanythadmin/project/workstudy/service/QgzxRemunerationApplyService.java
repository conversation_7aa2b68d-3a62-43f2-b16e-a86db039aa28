package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workstudy.dto.QgzxBatchWorkHoursDTO;
import com.sanythadmin.project.workstudy.dto.QgzxJobStudentDTO;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationApply;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationApplyParam;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 勤工助学报酬申报服务
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface QgzxRemunerationApplyService extends IService<QgzxRemunerationApply> {

    PageResult<QgzxRemunerationApply> pageList(QgzxRemunerationApplyParam param);
    PageResult<QgzxRemunerationApply> pageByEmployer(QgzxRemunerationApplyParam param);
    List<QgzxRemunerationApply> listApply(QgzxRemunerationApplyParam param);
    QgzxRemunerationApply getDetail(String id);
    void saveWithDetails(QgzxRemunerationApply remunerationApply);
    void updateWithDetails(QgzxRemunerationApply remunerationApply);
    void deleteWithDetails(String... ids);
    /**
     * 自动生成报酬申报（基于考勤记录）
     *
     * @param jobId     岗位ID
     * @param sbny      申报年月
     * @return 生成的申报信息
     */
    QgzxRemunerationApply generateFromAttendance(String jobId, String sbny);
    /**
     * 自动生成并保存报酬申报（基于考勤记录）
     * 根据岗位的bcsfsh字段判断：
     * - bcsfsh为"是"：需要走工作流申请审核
     * - bcsfsh为"否"：直接添加报酬，无需审核
     *
     * @param jobId     岗位ID
     * @param sbny      申报年月
     * @return 保存后的申报信息
     */
    QgzxRemunerationApply generateAndSaveFromAttendance(String jobId, String sbny);
    void checkIsEdit(String id);
    QgzxRemunerationApply getByJobIdAndSbny(String jobId, String sbny);
    void applyByEmployer(QgzxRemunerationApply remunerationApply);
    CompletableFuture<String> approve(WorkflowApprovalNodeRecord record, QgzxRemunerationApply remunerationApply, Executor executor);
    void delete(String... ids);
    PageResult<QgzxRemunerationApply> pageApprovalList(QgzxRemunerationApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);
    List<QgzxRemunerationApply> listApprovalList(QgzxRemunerationApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);
    <T> List<Map<String, Object>> distinct(SFunction<QgzxRemunerationApply, T> column);
    void addRemuneration(QgzxRemunerationApply remunerationApply);
    void removeRemuneration(String... ids);

    /**
     * 查询某个岗位下的用工学生名单
     *
     * @param jobId 岗位ID
     * @return 学生信息列表
     */
    List<QgzxJobStudentDTO> getJobStudentList(String jobId);

    /**
     * 批量设置工时并发起报酬申报
     *
     * @param batchWorkHoursDTO 批量工时设置数据
     */
    void applyByEmployerWithBatchWorkHours(QgzxBatchWorkHoursDTO batchWorkHoursDTO);
}
