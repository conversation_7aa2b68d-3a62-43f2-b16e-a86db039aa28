package com.sanythadmin.project.workstudy.dto;

import com.sanythadmin.project.workstudy.enums.WorkStatus;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 岗位下学生信息DTO
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Data
public class QgzxJobStudentDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学生申请ID
     */
    private String studentApplyId;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 学生姓名
     */
    private String xm;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 班级名称
     */
    private String bjmc;

    /**
     * 联系电话
     */
    private String sjh;

    /**
     * 用工状态
     */
    private WorkStatus ygzt;

    /**
     * 申请时间
     */
    private LocalDateTime sqsj;

    /**
     * 时薪
     */
    private BigDecimal hourlyRate;

    /**
     * 是否可以设置工时（用于前端判断）
     */
    private Boolean canSetWorkHours;

    /**
     * 备注
     */
    private String remark;
}
