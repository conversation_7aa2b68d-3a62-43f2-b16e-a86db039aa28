package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.enums.SysModule;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.entity.UserOrgMap;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.mapper.UserOrgMapMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.workflow.dto.ApprovalInfo;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNode;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowService;
import com.sanythadmin.project.workstudy.dto.QgzxBatchWorkHoursDTO;
import com.sanythadmin.project.workstudy.dto.QgzxJobStudentDTO;
import com.sanythadmin.project.workstudy.entity.*;
import com.sanythadmin.project.workstudy.enums.WorkStatus;
import com.sanythadmin.project.workstudy.mapper.*;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationApplyParam;
import com.sanythadmin.project.workstudy.param.QgzxStudentApplyParam;
import com.sanythadmin.project.workstudy.service.QgzxRemunerationApplyService;
import com.sanythadmin.project.workstudy.service.QgzxRemunerationDetailService;
import com.sanythadmin.project.workstudy.service.QgzxWorkHoursCalculationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 勤工助学报酬申报服务实现
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QgzxRemunerationApplyServiceImpl extends ServiceImpl<QgzxRemunerationApplyMapper, QgzxRemunerationApply> implements QgzxRemunerationApplyService {

    private final QgzxRemunerationApplyMapper remunerationApplyMapper;
    private final QgzxRemunerationDetailMapper remunerationDetailMapper;
    private final QgzxRemunerationDetailService remunerationDetailService;
    private final QgzxJobApplicationMapper jobApplicationMapper;
    private final QgzxStudentApplyMapper studentApplyMapper;
    private final QgzxEmployerMapper employerMapper;
    private final UserInfoMapper userInfoMapper;
    private final UserInfoService userInfoService;
    private final QgzxWorkHoursCalculationService workHoursCalculationService;
    private final WorkflowService workflowService;
    private final SysModuleSetupService sysModuleSetupService;

    @Override
    public PageResult<QgzxRemunerationApply> pageList(QgzxRemunerationApplyParam param) {
        MyMPJLambdaWrapper<QgzxRemunerationApply, QgzxRemunerationApplyParam> wrapper =
                new MyMPJLambdaWrapper<>(param);

        wrapper.selectAll(QgzxRemunerationApply.class)
                .selectAssociation("ja", QgzxJobApplication.class, QgzxRemunerationApply::getJobApplication)
                .leftJoin(QgzxJobApplication.class, "ja", QgzxJobApplication::getId, QgzxRemunerationApply::getJobId);
        Page<QgzxRemunerationApply> page = page(wrapper.getPage(), wrapper);
        return new PageResult<>(page.getRecords(),page.getTotal());
    }

    @Override
    public PageResult<QgzxRemunerationApply> pageByEmployer(QgzxRemunerationApplyParam param) {
        String currentUser = SecurityUtil.getUsername();
        LambdaQueryWrapper<QgzxEmployer> employerWrapper = new LambdaQueryWrapper<>();
        employerWrapper.eq(QgzxEmployer::getXgh, currentUser);
        QgzxEmployer employer = employerMapper.selectOne(employerWrapper);
        AssertUtil.isTrue(employer != null, "当前用户不是用人单位负责人");
        param.setEid(employer.getId());

        MyMPJLambdaWrapper<QgzxRemunerationApply, QgzxRemunerationApplyParam> wrapper =
                new MyMPJLambdaWrapper<>(param);

        wrapper.selectAll(QgzxRemunerationApply.class)
                .selectAssociation("ja", QgzxJobApplication.class, QgzxRemunerationApply::getJobApplication)
                .leftJoin(QgzxJobApplication.class, "ja", QgzxJobApplication::getId, QgzxRemunerationApply::getJobId)
                .eq("ja.EID", employer.getId());

        Page<QgzxRemunerationApply> page = page(wrapper.getPage(), wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<QgzxRemunerationApply> listApply(QgzxRemunerationApplyParam param) {
        MyMPJLambdaWrapper<QgzxRemunerationApply, QgzxRemunerationApplyParam> wrapper = 
                new MyMPJLambdaWrapper<>(param);
        
        wrapper.selectAll(QgzxRemunerationApply.class)
                .selectAssociation("ja", QgzxJobApplication.class, QgzxRemunerationApply::getJobApplication)
                .leftJoin(QgzxJobApplication.class, "ja", QgzxJobApplication::getId, QgzxRemunerationApply::getJobId);

        return list(wrapper);
    }

    @Override
    public QgzxRemunerationApply getDetail(String id) {
        QgzxRemunerationApply remunerationApply = getById(id);
        AssertUtil.isTrue(remunerationApply != null, "报酬申报不存在");
        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(remunerationApply.getJobId());
        remunerationApply.setJobApplication(jobApplication);
        List<QgzxRemunerationDetail> details = remunerationDetailService.getDetailsByApplyId(id);
        remunerationApply.setRemunerationDetails(details);
        
        return remunerationApply;
    }

    @Transactional
    @Override
    public void saveWithDetails(QgzxRemunerationApply remunerationApply) {
        remunerationApply.setXgh(SecurityUtil.getUsername());
        remunerationApply.setRoleId(SecurityUtil.getRoleId());
        remunerationApply.setSqsj(LocalDateTime.now());
        remunerationApply.setCreateTime(LocalDateTime.now());
        if (remunerationApply.getSpzt() == null) {
            remunerationApply.setSpzt(ReviewResult.DaiShenPi);
        }
        validateRemunerationApply(remunerationApply);
        save(remunerationApply);
        if (remunerationApply.getRemunerationDetails() != null) {
            for (QgzxRemunerationDetail detail : remunerationApply.getRemunerationDetails()) {
                detail.setRemunerationApplyId(remunerationApply.getId());
                detail.setCreateTime(LocalDateTime.now());
                remunerationDetailMapper.insert(detail);
            }
        }
    }

    @Transactional
    @Override
    public void updateWithDetails(QgzxRemunerationApply remunerationApply) {
        checkIsEdit(remunerationApply.getId());
        
        // 验证数据
        validateRemunerationApply(remunerationApply);
        
        // 更新申报记录
        remunerationApply.setUpdateTime(LocalDateTime.now());
        updateById(remunerationApply);
        
        // 删除原有明细
        remunerationDetailMapper.deleteByRemunerationApplyId(remunerationApply.getId());
        
        // 保存新明细
        if (remunerationApply.getRemunerationDetails() != null) {
            for (QgzxRemunerationDetail detail : remunerationApply.getRemunerationDetails()) {
                detail.setRemunerationApplyId(remunerationApply.getId());
                detail.setCreateTime(LocalDateTime.now());
                remunerationDetailMapper.insert(detail);
            }
        }
    }

    @Transactional
    @Override
    public void deleteWithDetails(String... ids) {
        for (String id : ids) {
            checkIsEdit(id);
            // 删除明细
            remunerationDetailMapper.deleteByRemunerationApplyId(id);
            // 删除申报记录
            removeById(id);
        }
    }

    @Override
    public QgzxRemunerationApply generateFromAttendance(String jobId, String sbny) {
        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(jobId);
        AssertUtil.isTrue(jobApplication != null, "岗位不存在");
        AssertUtil.isTrue(JudgeMark.YES.getText().equals(jobApplication.getSfqd().getText()), "该岗位未启用考勤打卡，无法自动生成");
        //TODO sbny当前月份
        sbny = "";
        LocalDate startDate = LocalDate.parse(sbny + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        Map<String, BigDecimal> workHoursMap = workHoursCalculationService.batchCalculateWorkHours(jobId, startDate, endDate);

        QgzxRemunerationApply remunerationApply = new QgzxRemunerationApply();
        remunerationApply.setJobId(jobId);
        remunerationApply.setXnxq(jobApplication.getXnxq());
        remunerationApply.setSbny(sbny);
        remunerationApply.setStartDate(startDate);
        remunerationApply.setEndDate(endDate);
        remunerationApply.setSbsm("基于考勤记录自动生成");

        List<QgzxRemunerationDetail> details = new ArrayList<>();
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal hourlyRate = jobApplication.getHourlyRate() != null ? BigDecimal.valueOf(jobApplication.getHourlyRate()) : BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> entry : workHoursMap.entrySet()) {
            String xgh = entry.getKey();
            BigDecimal workHours = entry.getValue();
            if (workHours.compareTo(BigDecimal.ZERO) > 0) {
                QgzxStudentApply studentApply = studentApplyMapper.selectOne(new LambdaQueryWrapper<QgzxStudentApply>()
                        .eq(QgzxStudentApply::getJobId, jobId)
                        .eq(QgzxStudentApply::getXgh, xgh));
                if (studentApply != null) {
                    QgzxRemunerationDetail detail = new QgzxRemunerationDetail();
                    detail.setStudentApplyId(studentApply.getId());
                    detail.setXgh(xgh);
                    detail.setHoursSourceType("AUTO_ATTENDANCE");
                    detail.setWorkHours(workHours);
                    detail.setHourlyRate(hourlyRate);
                    detail.setRemunerationAmount(workHours.multiply(hourlyRate));
                    detail.setRemark("基于考勤记录自动计算");

                    details.add(detail);
                    totalHours = totalHours.add(workHours);
                    totalAmount = totalAmount.add(detail.getRemunerationAmount());
                }
            }
        }

        remunerationApply.setTotalHours(totalHours);
        remunerationApply.setTotalAmount(totalAmount);
        remunerationApply.setRemunerationDetails(details);

        return remunerationApply;
    }

    @Transactional
    @Override
    public QgzxRemunerationApply generateAndSaveFromAttendance(String jobId, String sbny) {
        QgzxRemunerationApply remunerationApply = generateFromAttendance(jobId, sbny);

        QgzxRemunerationApply existingApply = getByJobIdAndSbny(jobId, sbny);
        AssertUtil.isTrue(existingApply == null ||
                (!ReviewResult.DaiShenPi.equals(existingApply.getSpzt()) && !ReviewResult.TongGuo.equals(existingApply.getSpzt())),
                "该岗位在" + sbny + "已有报酬申报记录，请勿重复提交");
        applyByEmployer(remunerationApply);
        return remunerationApply;
    }

    @Override
    public void checkIsEdit(String id) {
        QgzxRemunerationApply remunerationApply = getById(id);
        AssertUtil.isTrue(remunerationApply != null, "报酬申报不存在");
        AssertUtil.isTrue(ReviewResult.DaiShenPi.equals(remunerationApply.getSpzt()), "只有待审批状态的申报才能编辑或删除");
    }

    @Override
    public QgzxRemunerationApply getByJobIdAndSbny(String jobId, String sbny) {
        return remunerationApplyMapper.selectByJobIdAndSbny(jobId, sbny);
    }

    /**
     * 验证报酬申报数据
     */
    private void validateRemunerationApply(QgzxRemunerationApply remunerationApply) {
        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(remunerationApply.getJobId());
        AssertUtil.isTrue(jobApplication != null, "岗位不存在");
        
        // 验证工时和报酬限制
        if (remunerationApply.getRemunerationDetails() != null) {
            for (QgzxRemunerationDetail detail : remunerationApply.getRemunerationDetails()) {
                // 验证学生工时限制
                boolean validStudentHours = workHoursCalculationService.validateStudentWorkHours(
                        detail.getXgh(), remunerationApply.getJobId(), detail.getWorkHours(), remunerationApply.getSbny());
                AssertUtil.isTrue(validStudentHours, "学生 " + detail.getXgh() + " 的工时超出岗位限制");
            }
        }
        
        // 验证岗位总工时限制
        boolean validJobTotalHours = workHoursCalculationService.validateJobTotalWorkHours(
                remunerationApply.getJobId(), remunerationApply.getTotalHours(), remunerationApply.getSbny());
        AssertUtil.isTrue(validJobTotalHours, "岗位总工时超出岗位类别工时限制");
        
        // 验证岗位类别总报酬限制
        boolean validJobTypeTotalAmount = workHoursCalculationService.validateJobTypeTotalAmount(
                jobApplication.getJobTypeId(), remunerationApply.getTotalAmount(), remunerationApply.getSbny());
        AssertUtil.isTrue(validJobTypeTotalAmount, "岗位总报酬超出岗位类别报酬限制");
    }

    @Transactional
    @Override
    public void applyByEmployer(QgzxRemunerationApply remunerationApply) {
        UserInfo userInfo = userInfoService.get(SecurityUtil.getUsername());
        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(remunerationApply.getJobId());
        AssertUtil.isTrue(jobApplication != null, "岗位信息不存在");

        // 检查是否已有同月申报记录
        QgzxRemunerationApply existingApply = getByJobIdAndSbny(remunerationApply.getJobId(), remunerationApply.getSbny());
        AssertUtil.isTrue(existingApply == null ||
                (!ReviewResult.DaiShenPi.equals(existingApply.getSpzt()) && !ReviewResult.TongGuo.equals(existingApply.getSpzt())),
                "该岗位在" + remunerationApply.getSbny() + "已有报酬申报记录，请勿重复提交");

        remunerationApply.setJobApplication(jobApplication);
        remunerationApply.initRequiredFields();

        remunerationApply.setCreateTime(LocalDateTime.now());
        remunerationApply.setSqsj(LocalDateTime.now());

        validateRemunerationApply(remunerationApply);

        if (JudgeMark.YES.equals(jobApplication.getBcsfsh())) {
            remunerationApply.setSpzt(ReviewResult.DaiShenPi);

            saveWithDetails(remunerationApply);

            String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
            String workflowId = workflowService.createApprovalNode(WorkflowApprovalNode.class, remunerationApply,
                    new WorkflowParam(null, year, SysModule.qgzxBcsb.getCode()), userInfo);
            remunerationApply.setWorkflowId(workflowId);
            updateById(remunerationApply);
            log.info("报酬申报提交审核成功: 申报ID={}, 岗位ID={}, 申请人={}",remunerationApply.getId(), remunerationApply.getJobId(), userInfo.getXgh());
        } else {
            remunerationApply.setSpzt(ReviewResult.TongGuo);
            saveWithDetails(remunerationApply);
            log.info("报酬直接添加成功: 申报ID={}, 岗位ID={}, 申请人={}",remunerationApply.getId(), remunerationApply.getJobId(), userInfo.getXgh());
        }
    }

    @Transactional
    @Override
    public CompletableFuture<String> approve(WorkflowApprovalNodeRecord record, QgzxRemunerationApply remunerationApply, Executor executor) {
        final QgzxRemunerationApply info = remunerationApply == null ? remunerationApplyMapper.selectById(record.getApplicationId()) : remunerationApply;
        return CompletableFuture.supplyAsync(() -> {
            UserInfo userInfo = userInfoMapper.get(info.getXgh());

            ApprovalInfo<WorkflowApprovalNode, WorkflowApprovalNodeRecord> approvalInfo =
                    workflowService.approve(WorkflowApprovalNode.class, record, info, new WorkflowParam(info.getWorkflowId()), userInfo);
            if(JudgeMark.YES.getText().equals(approvalInfo.getCurrentApprovalNode().getEndNode().getText()) &&
                    ReviewResult.TongGuo.getText().equals(approvalInfo.getReviewResult().getText())){
                //TODO 审核通过,发放报酬
            }
            remunerationApplyMapper.update(new LambdaUpdateWrapper<QgzxRemunerationApply>()
                    .set(QgzxRemunerationApply::getSpzt, approvalInfo.getReviewResult())
                    .eq(QgzxRemunerationApply::getId, info.getId()));
            return approvalInfo.getNextApprovableNodeId();
        }, executor);
    }

    @Override
    public void delete(String... ids) {
        for (String id : ids) {
            QgzxRemunerationApply remunerationApply = getById(id);
            AssertUtil.isTrue(remunerationApply != null, "报酬申报不存在：" + id);
            checkIsEdit(id);
        }
        deleteWithDetails(ids);
    }

    @Override
    public PageResult<QgzxRemunerationApply> pageApprovalList(QgzxRemunerationApplyParam param,
                                                              UserInfoParam userInfoParam,
                                                              WorkflowApprovalNodeParam approvalNodeParam) {
        // 构建查询条件
        MyMPJLambdaWrapper<QgzxRemunerationApply, QgzxRemunerationApplyParam> wrapper =
            buildApprovalListWrapper(param, userInfoParam, approvalNodeParam);

        // 分页查询
        Page<QgzxRemunerationApply> page = wrapper.getPage();
        page = page(page, wrapper);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<QgzxRemunerationApply> listApprovalList(QgzxRemunerationApplyParam param,
                                                        UserInfoParam userInfoParam,
                                                        WorkflowApprovalNodeParam approvalNodeParam) {
        // 构建查询条件
        MyMPJLambdaWrapper<QgzxRemunerationApply, QgzxRemunerationApplyParam> wrapper =
            buildApprovalListWrapper(param, userInfoParam, approvalNodeParam);

        return list(wrapper);
    }

    @Override
    public <T> List<Map<String, Object>> distinct(SFunction<QgzxRemunerationApply, T> column) {
        return remunerationApplyMapper.selectMaps(new LambdaQueryWrapper<QgzxRemunerationApply>()
                .select(column)
                .groupBy(column)
                .orderByDesc(column));
    }

    /**
     * 构建审核列表查询条件
     */
    private MyMPJLambdaWrapper<QgzxRemunerationApply, QgzxRemunerationApplyParam> buildApprovalListWrapper(
            QgzxRemunerationApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam) {

        MyMPJLambdaWrapper<QgzxRemunerationApply, QgzxRemunerationApplyParam> wrapper =
                new MyMPJLambdaWrapper<>(param);

        wrapper.selectAssociation("ja", QgzxJobApplication.class, QgzxRemunerationApply::getJobApplication)
                .leftJoin(QgzxJobApplication.class, "ja", QgzxJobApplication::getId, QgzxRemunerationApply::getJobId)
                .selectAssociation("u", UserInfo.class, QgzxRemunerationApply::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxRemunerationApply::getXgh);

        wrapper.selectAll(QgzxRemunerationApply.class);

        if (StringUtils.isEmpty(param.getXnxq())) {
            param.setXnxq(sysModuleSetupService.getModuleYear(SysModule.qgzxBcsb.getCode()));
        }

        return wrapper;
    }

    @Transactional
    @Override
    public void addRemuneration(QgzxRemunerationApply remunerationApply) {
        UserInfo userInfo = userInfoService.get(SecurityUtil.getUsername());
        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(remunerationApply.getJobId());
        AssertUtil.isTrue(jobApplication != null, "岗位信息不存在");

        QgzxRemunerationApply existingApply = getByJobIdAndSbny(remunerationApply.getJobId(), remunerationApply.getSbny());
        AssertUtil.isTrue(existingApply == null ||
                (!ReviewResult.DaiShenPi.equals(existingApply.getSpzt()) && !ReviewResult.TongGuo.equals(existingApply.getSpzt())),
                "该岗位在" + remunerationApply.getSbny() + "已有报酬申报记录，请勿重复提交");
        remunerationApply.setJobApplication(jobApplication);
        remunerationApply.initRequiredFields();
        remunerationApply.setCreateTime(LocalDateTime.now());
        remunerationApply.setSqsj(LocalDateTime.now());
        validateRemunerationApply(remunerationApply);
        remunerationApply.setSpzt(ReviewResult.TongGuo);
        saveWithDetails(remunerationApply);

        log.info("添加报酬成功: 申报ID={}, 岗位ID={}, 操作人={}", remunerationApply.getId(), remunerationApply.getJobId(), userInfo.getXgh());
    }

    @Transactional
    @Override
    public void removeRemuneration(String... ids) {
        UserInfo userInfo = userInfoService.get(SecurityUtil.getUsername());
        for (String id : ids) {
            QgzxRemunerationApply remunerationApply = getById(id);
            AssertUtil.isTrue(remunerationApply != null, "报酬申报不存在：" + id);
            AssertUtil.isTrue(ReviewResult.TongGuo.equals(remunerationApply.getSpzt()),"资助中心只能删除已通过状态的报酬申报");
        }
        deleteWithDetails(ids);
        log.info("资助中心删除报酬成功: 申报IDs={}, 操作人={}",String.join(",", ids), userInfo.getXgh());
    }

    @Override
    public List<QgzxJobStudentDTO> getJobStudentList(String jobId) {
        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(jobId);
        AssertUtil.isTrue(jobApplication != null, "岗位不存在");
        MyMPJLambdaWrapper<QgzxStudentApply, QgzxStudentApplyParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.selectAll(QgzxStudentApply.class)
                .selectAssociation("u", UserInfo.class, QgzxStudentApply::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh)
                .leftJoin(UserOrgMap.class, "uom", UserOrgMap::getXgh, UserInfo::getXgh)
                .selectCollection(UserOrgMap.class, UserInfo::getUserOrgMaps)
                .eq(QgzxStudentApply::getJobId, jobId)
                .eq(QgzxStudentApply::getYgzt, WorkStatus.YGJS)
                .orderByDesc(QgzxStudentApply::getSqsj);

        List<QgzxStudentApply> studentApplies = studentApplyMapper.selectJoinList(QgzxStudentApply.class, wrapper);
        UserInfoUtil.codeTextSet(studentApplies);
        List<QgzxJobStudentDTO> result = new ArrayList<>();
        for (QgzxStudentApply studentApply : studentApplies) {
            UserInfo userInfo = studentApply.getUserInfo();
            if (userInfo != null) {
                QgzxJobStudentDTO dto = new QgzxJobStudentDTO();
                dto.setStudentApplyId(studentApply.getId());
                dto.setXgh(studentApply.getXgh());
                dto.setXm(userInfo.getXm());
                dto.setXymc(userInfo.getXymc());
                dto.setZymc(userInfo.getZymc());
                dto.setBjmc(userInfo.getBjmc());
                dto.setSjh(userInfo.getSjh());
                dto.setYgzt(studentApply.getYgzt());
                dto.setSqsj(studentApply.getSqsj());
                dto.setHourlyRate(jobApplication.getHourlyRate() != null ? BigDecimal.valueOf(jobApplication.getHourlyRate()) : BigDecimal.ZERO);
                result.add(dto);
            }
        }

        return result;
    }

    @Transactional
    @Override
    public void applyByEmployerWithBatchWorkHours(QgzxBatchWorkHoursDTO batchWorkHoursDTO) {
        AssertUtil.isTrue(batchWorkHoursDTO != null, "批量工时数据不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(batchWorkHoursDTO.getJobId()), "岗位ID不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(batchWorkHoursDTO.getSbny()), "申报年月不能为空");
        AssertUtil.isTrue(batchWorkHoursDTO.getStudentWorkHours() != null && !batchWorkHoursDTO.getStudentWorkHours().isEmpty(),
                "学生工时明细不能为空");

        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(batchWorkHoursDTO.getJobId());
        AssertUtil.isTrue(jobApplication != null, "岗位不存在");

        QgzxRemunerationApply existingApply = getByJobIdAndSbny(batchWorkHoursDTO.getJobId(), batchWorkHoursDTO.getSbny());
        AssertUtil.isTrue(existingApply == null ||
                (!ReviewResult.DaiShenPi.equals(existingApply.getSpzt()) && !ReviewResult.TongGuo.equals(existingApply.getSpzt())),
                "该岗位在" + batchWorkHoursDTO.getSbny() + "已有报酬申报记录，请勿重复提交");

        QgzxRemunerationApply remunerationApply = new QgzxRemunerationApply();
        remunerationApply.setJobId(batchWorkHoursDTO.getJobId());
        remunerationApply.setXnxq(jobApplication.getXnxq());
        remunerationApply.setSbny(batchWorkHoursDTO.getSbny());
        remunerationApply.setSbsm(StringUtils.isNotBlank(batchWorkHoursDTO.getSbsm()) ?
                batchWorkHoursDTO.getSbsm() : "用人单位批量设置工时申报");

        LocalDate startDate = LocalDate.parse(batchWorkHoursDTO.getSbny() + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        remunerationApply.setStartDate(startDate);
        remunerationApply.setEndDate(endDate);

        List<QgzxRemunerationDetail> details = new ArrayList<>();
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (QgzxBatchWorkHoursDTO.StudentWorkHoursDetail studentDetail : batchWorkHoursDTO.getStudentWorkHours()) {
            QgzxStudentApply studentApply = studentApplyMapper.selectById(studentDetail.getStudentApplyId());
            AssertUtil.isTrue(studentApply != null, "学生申请不存在：" + studentDetail.getStudentApplyId());
            AssertUtil.isTrue(batchWorkHoursDTO.getJobId().equals(studentApply.getJobId()),
                    "学生申请的岗位ID与当前岗位ID不匹配：" + studentDetail.getXgh());

            AssertUtil.isTrue(studentDetail.getWorkHours() != null && studentDetail.getWorkHours().compareTo(BigDecimal.ZERO) > 0,
                    "学生工时必须大于0：" + studentDetail.getXgh());
            AssertUtil.isTrue(studentDetail.getHourlyRate() != null && studentDetail.getHourlyRate().compareTo(BigDecimal.ZERO) > 0,
                    "时薪必须大于0：" + studentDetail.getXgh());

            QgzxRemunerationDetail detail = new QgzxRemunerationDetail();
            detail.setStudentApplyId(studentDetail.getStudentApplyId());
            detail.setXgh(studentDetail.getXgh());
            detail.setHoursSourceType("MANUAL_INPUT");
            detail.setWorkHours(studentDetail.getWorkHours());
            detail.setHourlyRate(studentDetail.getHourlyRate());
            detail.setRemunerationAmount(studentDetail.getRemunerationAmount() != null ?
                    studentDetail.getRemunerationAmount() :
                    studentDetail.getWorkHours().multiply(studentDetail.getHourlyRate()));
            detail.setRemark(StringUtils.isNotBlank(studentDetail.getRemark()) ?
                    studentDetail.getRemark() : "用人单位手动设置工时");

            details.add(detail);
            totalHours = totalHours.add(studentDetail.getWorkHours());
            totalAmount = totalAmount.add(detail.getRemunerationAmount());
        }

        remunerationApply.setTotalHours(totalHours);
        remunerationApply.setTotalAmount(totalAmount);
        remunerationApply.setRemunerationDetails(details);

        applyByEmployer(remunerationApply);

        log.info("批量设置工时并发起报酬申报成功: 岗位ID={}, 申报年月={}, 学生数量={}, 操作人={}",
                batchWorkHoursDTO.getJobId(), batchWorkHoursDTO.getSbny(),
                batchWorkHoursDTO.getStudentWorkHours().size(), SecurityUtil.getUsername());
    }
}
