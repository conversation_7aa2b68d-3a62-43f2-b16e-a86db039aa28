package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysAccountRole;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.SysAccountRoleMapper;
import com.sanythadmin.common.system.mapper.SysRoleMapper;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import com.sanythadmin.project.workflow.dto.*;
import com.sanythadmin.project.workflow.entity.*;
import com.sanythadmin.project.workflow.enums.*;
import com.sanythadmin.project.workflow.mapper.*;
import com.sanythadmin.project.workflow.observer.WorkflowNodeEventSubscription;
import com.sanythadmin.project.workflow.observer.WorkflowNodeFormEventSubscription;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowService;
import com.sanythadmin.project.workflow.util.WorkflowUtil;
import dm.jdbc.util.StringUtil;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工作流Service实现
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WorkflowServiceImpl extends ServiceImpl<WorkflowMapper, Workflow> implements WorkflowService {
    private final WorkflowMapper workflowMapper;
    private final WorkflowNodeMapper workflowNodeMapper;
    private final WorkflowNodeApproverMapper workflowNodeApproverMapper;
    private final WorkflowConditionMapper conditionMapper;
    private final WorkflowNodeFormMapper workflowNodeFormMapper;
    private final WorkflowNodeStateMapper workflowNodeStateMapper;
    private final WorkflowConditionDetailMapper conditionDetailMapper;
    private final WorkflowNodeEventSubscription workflowNodeEventSubscription;
    private final WorkflowNodeFormEventSubscription workflowNodeFormEventSubscription;
    private final SysRoleMapper sysRoleMapper;
    private final UserInfoService userInfoService;
    private final SysAccountRoleMapper accountRoleMapper;
    private final SysModuleSetupService sysModuleSetupService;
    private final WorkflowApprovalNodeRecordMapper workflowApprovalNodeRecordMapper;
    private final WorkflowApprovalNodeMapper workflowApprovalNodeMapper;

    @Transactional
    @Override
    public void editWorkflow(WorkflowDTO workflow) {
        workflow.format();
        List<NodeDTO> nodes = workflow.getNodes();
        List<ConditionDTO> conditions = workflow.getConditions();
        checkNodeInfo(nodes, conditions);
        List<String> removeConditionIds = new ArrayList<>();
        if (StringUtils.hasText(workflow.getModuleCode())) {
            workflow.setYear(sysModuleSetupService.getModuleYear(workflow.getModuleCode()));
        } else {
            workflow.setYear(null);
        }
        if (StringUtils.hasText(workflow.getId())) {
            Workflow workflow1 = getById(workflow.getId());
            workflow.setCreateTime(workflow1.getCreateTime());
            workflowMapper.updateById(workflow);
            List<String> updateNodeIds = nodes.stream().map(WorkflowNode::getId).filter(StringUtils::hasText).collect(Collectors.toList());
            List<WorkflowNode> removeNodes = workflowNodeMapper.selectList(new LambdaQueryWrapper<WorkflowNode>().eq(WorkflowNode::getWorkflowId, workflow.getId()).notIn(WorkflowNode::getId, updateNodeIds));
            for (WorkflowNode node : removeNodes) {
                workflowNodeApproverMapper.delete(new LambdaQueryWrapper<WorkflowNodeApprover>().eq(WorkflowNodeApprover::getNodeId, node.getId()));
                workflowNodeFormMapper.delete(new LambdaQueryWrapper<WorkflowNodeForm>().eq(WorkflowNodeForm::getNodeId, node.getId()));
                workflowNodeMapper.delete(new LambdaQueryWrapper<WorkflowNode>().eq(WorkflowNode::getId, node.getId()));
                workflowNodeStateMapper.delete(new LambdaQueryWrapper<WorkflowNodeState>().eq(WorkflowNodeState::getNodeId, node.getId()));
                if (StringUtils.hasText(node.getConditionId())) {
                    String[] idArray = CommonUtil.split(node.getConditionId());
                    removeConditionIds.addAll(Arrays.asList(idArray));
                    conditionMapper.delete(new LambdaQueryWrapper<WorkflowCondition>().eq(WorkflowCondition::getWorkflowId, workflow.getId()).in(WorkflowCondition::getId, (Object[]) idArray));
                    conditionDetailMapper.delete(new LambdaQueryWrapper<WorkflowConditionDetail>().eq(WorkflowConditionDetail::getWorkflowId, workflow.getId()).in(WorkflowConditionDetail::getConditionId, (Object[]) idArray));
                }
            }
        } else {
            workflow.setCreateTime(LocalDateTime.now());
            workflowMapper.insert(workflow);
        }

        List<String> idList = new ArrayList<>();
        List<String> idList1 = new ArrayList<>();
        for (ConditionDTO condition : conditions) {
            if (StringUtils.hasText(condition.getId()) && removeConditionIds.contains(condition.getId())) {
                condition.setId(null);
                List<WorkflowConditionDetail> detailList = condition.getConditionDetail();
                for (WorkflowConditionDetail detail : detailList) {
                    detail.setId(null);
                    detail.setConditionId(null);
                }
            }
        }

        HashMap<String, String> conditionMap = new HashMap<>();
        for (ConditionDTO conditionDTO : conditions) {
            WorkflowCondition condition = new WorkflowCondition();
            BeanUtils.copyProperties(conditionDTO, condition);
            condition.setWorkflowId(workflow.getId());
            if (StringUtils.hasText(condition.getId())) {
                conditionMapper.updateById(condition);
            } else {
                conditionMapper.insert(condition);
            }
            idList.add(condition.getId());
            conditionMap.put(condition.getConditionName(), condition.getId());
            List<WorkflowConditionDetail> conditionDetails = conditionDTO.getConditionDetail();
            int num = 1;
            for (WorkflowConditionDetail detail : conditionDetails) {
                detail.setConditionId(condition.getId());
                detail.setWorkflowId(workflow.getId());
                detail.setSort(num++);
                if (StringUtils.hasText(detail.getId())) {
                    conditionDetailMapper.updateById(detail);
                } else {
                    conditionDetailMapper.insert(detail);
                }
                idList1.add(detail.getId());
            }
        }

        if (!CollectionUtils.isEmpty(idList))
            conditionMapper.delete(new LambdaQueryWrapper<WorkflowCondition>().eq(WorkflowCondition::getWorkflowId, workflow.getId()).notIn(WorkflowCondition::getId, idList));
        if (!CollectionUtils.isEmpty(idList1))
            conditionDetailMapper.delete(new LambdaQueryWrapper<WorkflowConditionDetail>().eq(WorkflowConditionDetail::getWorkflowId, workflow.getId()).notIn(WorkflowConditionDetail::getId, idList1));

        for (WorkflowNode node : nodes) {
            node.setWorkflowId(workflow.getId());
            String conditionName = node.getConditionName();
            if (StringUtils.hasText(conditionName)) {
                String[] strings = CommonUtil.split(conditionName);
                StringBuilder conditionId = new StringBuilder();
                for (String str : strings) {
                    conditionId.append(conditionMap.get(str)).append(",");
                }
                if (!conditionId.isEmpty()) conditionId.deleteCharAt(conditionId.length() - 1);
                node.setConditionId(conditionId.toString());
            }
            if (StringUtils.hasText(node.getId())) {
                workflowNodeMapper.updateById(node);
            } else {
                workflowNodeMapper.insert(node);
            }
        }

        idList.clear();
        idList1.clear();
        List<String> roles = sysRoleMapper.selectObjs(new LambdaQueryWrapper<SysRole>().select(SysRole::getId));
        for (NodeDTO node : nodes) {
            if (!node.getType().getValue().equals(NodeType.JieDian.getValue())) continue;
            List<WorkflowNodeApprover> nodeApprovers = node.getWorkflowNodeApprovers();
            for (WorkflowNodeApprover nodeApprover : nodeApprovers) {
                nodeApprover.setNodeId(node.getId());
                nodeApprover.setWorkflowId(workflow.getId());
                ApproverType approverType = roles.contains(nodeApprover.getApproverId()) ? ApproverType.role : ApproverType.user;
                nodeApprover.setApproverType(approverType);
                if (StringUtils.hasText(nodeApprover.getId())) {
                    workflowNodeApproverMapper.updateById(nodeApprover);
                } else {
                    workflowNodeApproverMapper.insert(nodeApprover);
                }
                idList.add(nodeApprover.getId());
                List<WorkflowNodeForm> nodeFormList = nodeApprover.getWorkflowNodeForm();
                if (!CollectionUtils.isEmpty(nodeFormList)) {
                    for (WorkflowNodeForm form : nodeFormList) {
                        form.setNodeId(node.getId());
                        form.setWorkflowId(workflow.getId());
                        form.setNodeApproverId(nodeApprover.getId());
                        if (StringUtils.hasText(form.getId())) {
                            workflowNodeFormMapper.updateById(form);
                        } else {
                            workflowNodeFormMapper.insert(form);
                        }
                        idList1.add(form.getId());
                    }
                }
            }

            workflowNodeStateMapper.delete(new LambdaQueryWrapper<WorkflowNodeState>().eq(WorkflowNodeState::getNodeId, node.getId()).eq(WorkflowNodeState::getWorkflowId, workflow.getId()));
            WorkflowNodeState nodeState = node.getWorkflowNodeState();
            if (nodeState != null) {
                nodeState.setId(null);
                nodeState.setNodeId(node.getId());
                nodeState.setWorkflowId(workflow.getId());
                workflowNodeStateMapper.insert(nodeState);
            }
        }

        if (!CollectionUtils.isEmpty(idList))
            workflowNodeApproverMapper.delete(new LambdaQueryWrapper<WorkflowNodeApprover>().eq(WorkflowNodeApprover::getWorkflowId, workflow.getId()).notIn(WorkflowNodeApprover::getId, idList));
        if (!CollectionUtils.isEmpty(idList1))
            workflowNodeFormMapper.delete(new LambdaQueryWrapper<WorkflowNodeForm>().eq(WorkflowNodeForm::getWorkflowId, workflow.getId()).notIn(WorkflowNodeForm::getId, idList1));
    }

    private void checkNodeInfo(List<NodeDTO> list, List<ConditionDTO> conditions) {
        if (CollectionUtils.isEmpty(list)) AssertUtil.throwMessage("工作流节点不能为空");
        HashSet<String> hashSet = new HashSet<>();
        for (NodeDTO node : list) {
            if (Objects.equals(node.getType().getValue(), NodeType.JieDian.getValue())) {
                if (hashSet.contains(node.getNodeName()))
                    AssertUtil.throwMessage(node.getNodeName() + "节点名称不能重复");
                hashSet.add(node.getNodeName());
                if (CollectionUtils.isEmpty(node.getWorkflowNodeApprovers()))
                    AssertUtil.throwMessage(node.getNodeName() + "节点审批对象不能为空");
            }
        }

        hashSet.clear();
        for (ConditionDTO condition : conditions) {
            String conditionName = condition.getConditionName();
            if (!StringUtils.hasText(conditionName)) AssertUtil.throwMessage("条件名称不能为空");
            if (hashSet.contains(conditionName)) {
                AssertUtil.throwMessage(conditionName + "条件名称不能重复");
            } else {
                hashSet.add(conditionName);
            }
        }
    }

    @Override
    public void copyWorkflow(WorkflowDTO workflow, String projectId, String year, String name) {
        workflow.format();
        workflow.setProjectId(projectId);
        workflow.setYear(year);
        Workflow object = workflowMapper.selectOne(new LambdaQueryWrapper<Workflow>().eq(Workflow::getProjectId, projectId).eq(Workflow::getYear, year));
        if (!Objects.isNull(object)) return;
        workflow.setName(workflow.getName() + "_" + name);
        List<NodeDTO> nodes = workflow.getNodes();
        List<ConditionDTO> conditions = workflow.getConditions();
        workflow.setId(null);
        for (NodeDTO node : nodes) {
            node.setId(null);
            node.setWorkflowId(null);
            node.setConditionId(null);
            List<WorkflowNodeApprover> nodeApprovers = node.getWorkflowNodeApprovers();
            if (!CollectionUtils.isEmpty(nodeApprovers)) {
                nodeApprovers.forEach(approver -> {
                    approver.setId(null);
                    approver.setNodeId(null);
                    approver.setWorkflowId(null);
                    List<WorkflowNodeForm> nodeForms = approver.getWorkflowNodeForm();
                    if (!CollectionUtils.isEmpty(nodeForms)) {
                        nodeForms.forEach(form -> {
                            form.setId(null);
                            form.setNodeId(null);
                            form.setNodeApproverId(null);
                            form.setWorkflowId(null);
                        });
                    }
                });
            }

            WorkflowNodeState nodeState = node.getWorkflowNodeState();
            if (!Objects.isNull(nodeState)) {
                nodeState.setId(null);
                nodeState.setNodeId(null);
                nodeState.setWorkflowId(null);
            }
        }

        if (!CollectionUtils.isEmpty(conditions)) {
            conditions.forEach(condition -> {
                condition.setId(null);
                condition.setWorkflowId(null);
                List<WorkflowConditionDetail> conditionDetails = condition.getConditionDetail();
                if (!CollectionUtils.isEmpty(conditionDetails)) {
                    conditionDetails.forEach(conditionDetail -> {
                        conditionDetail.setId(null);
                        conditionDetail.setConditionId(null);
                        conditionDetail.setWorkflowId(null);
                    });
                }
            });
        }
    }

    @Override
    public void copyWorkflow(Workflow workflow, String projectId, String year) {
        String workflowId = workflow.getId();
        List<WorkflowNode> nodeList = workflowNodeMapper.selectList(new LambdaQueryWrapper<WorkflowNode>().eq(WorkflowNode::getWorkflowId, workflowId));
        workflow.setId(null);
        workflow.setProjectId(projectId);
        workflow.setYear(year);
        workflow.setCreateTime(LocalDateTime.now());
        workflowMapper.insert(workflow);

        HashMap<String, String> conditionIdMap = new HashMap<>();
        List<WorkflowCondition> conditions = conditionMapper.selectList(new LambdaQueryWrapper<WorkflowCondition>().eq(WorkflowCondition::getWorkflowId, workflowId));
        if (!CollectionUtils.isEmpty(conditions)) {
            conditions.forEach(condition -> {
                String id = condition.getId();
                condition.setId(null);
                condition.setWorkflowId(workflow.getId());
                conditionMapper.insert(condition);
                conditionIdMap.put(id, condition.getId());
                List<WorkflowConditionDetail> conditionDetails = conditionDetailMapper.selectList(new LambdaQueryWrapper<WorkflowConditionDetail>().eq(WorkflowConditionDetail::getConditionId, id));
                if (!CollectionUtils.isEmpty(conditionDetails)) {
                    conditionDetails.forEach(conditionDetail -> {
                        conditionDetail.setId(null);
                        conditionDetail.setWorkflowId(workflow.getId());
                        conditionDetail.setConditionId(condition.getId());
                        conditionDetailMapper.insert(conditionDetail);
                    });
                }
            });
        }

        HashMap<String, String> nodeMap = new HashMap<>();
        nodeList.forEach(node -> {
            String nodeId = node.getId();
            node.setId(null);
            node.setWorkflowId(workflow.getId());
            if (StringUtils.hasText(node.getConditionId())) {
                String[] strings = CommonUtil.split(node.getConditionId());
                List<String> conditionIds = new ArrayList<>();
                for (String str : strings) {
                    conditionIds.add(conditionIdMap.get(str));
                }
                node.setConditionId(String.join(",", conditionIds));
            }
            workflowNodeMapper.insert(node);
            nodeMap.put(nodeId, node.getId());
            nodeMap.put(node.getId(), nodeId);
        });

        for (WorkflowNode node : nodeList) {
            String nodeId = nodeMap.get(node.getId());
            HashMap<String, String> nodeApproverMap = new HashMap<>();
            List<WorkflowNodeApprover> nodeApprovers = workflowNodeApproverMapper.selectList(new LambdaQueryWrapper<WorkflowNodeApprover>().eq(WorkflowNodeApprover::getNodeId, nodeId).eq(WorkflowNodeApprover::getWorkflowId, workflowId));
            nodeApprovers.forEach(approver -> {
                String id = approver.getId();
                approver.setId(null);
                approver.setWorkflowId(workflow.getId());
                approver.setNodeId(node.getId());
                workflowNodeApproverMapper.insert(approver);
                nodeApproverMap.put(id, approver.getId());
            });

            List<WorkflowNodeForm> nodeForms = workflowNodeFormMapper.selectList(new LambdaQueryWrapper<WorkflowNodeForm>().eq(WorkflowNodeForm::getNodeId, nodeId).eq(WorkflowNodeForm::getWorkflowId, workflowId));
            if (!CollectionUtils.isEmpty(nodeForms)) {
                nodeForms.forEach(nodeForm -> {
                    nodeForm.setId(null);
                    nodeForm.setWorkflowId(workflow.getId());
                    nodeForm.setNodeId(node.getId());
                    nodeForm.setNodeApproverId(nodeApproverMap.get(nodeForm.getNodeApproverId()));
                    workflowNodeFormMapper.insert(nodeForm);
                });
            }

            List<WorkflowNodeState> nodeStates = workflowNodeStateMapper.selectList(new LambdaQueryWrapper<WorkflowNodeState>().eq(WorkflowNodeState::getWorkflowId, workflowId).eq(WorkflowNodeState::getNodeId, nodeId));
            if (!CollectionUtils.isEmpty(nodeStates)) {
                nodeStates.forEach(nodeState -> {
                    nodeState.setId(null);
                    nodeState.setWorkflowId(workflow.getId());
                    nodeState.setNodeId(node.getId());
                    workflowNodeStateMapper.insert(nodeState);
                });
            }
        }
    }

    @Transactional
    @Override
    public void removeWorkflow(List<String> ids) {
//        for (String id : ids) {
//            Workflow workflow = getById(id);
//            if (StringUtils.hasText(workflow.getProjectId())) AssertUtil.throwMessage("删除失败, 工作流已被项目引用");
//        }

        for (String id : ids) {
            workflowNodeMapper.delete(new LambdaQueryWrapper<WorkflowNode>().eq(WorkflowNode::getWorkflowId, id));
            workflowNodeApproverMapper.delete(new LambdaQueryWrapper<WorkflowNodeApprover>().eq(WorkflowNodeApprover::getWorkflowId, id));
            conditionMapper.delete(new LambdaQueryWrapper<WorkflowCondition>().eq(WorkflowCondition::getWorkflowId, id));
            conditionDetailMapper.delete(new LambdaQueryWrapper<WorkflowConditionDetail>().eq(WorkflowConditionDetail::getWorkflowId, id));
            workflowNodeFormMapper.delete(new LambdaQueryWrapper<WorkflowNodeForm>().eq(WorkflowNodeForm::getWorkflowId, id));
            workflowNodeStateMapper.delete(new LambdaQueryWrapper<WorkflowNodeState>().eq(WorkflowNodeState::getWorkflowId, id));
        }
        workflowMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional
    public void terminateWorkflow(String workflowId, String reason) {
        AssertUtil.isTrue(StringUtil.isNotEmpty(workflowId), "工作流ID不能为空");
        try {
            Workflow workflow = getById(workflowId);
            AssertUtil.isTrue(workflow != null, "工作流不存在: " + workflowId);
            List<WorkflowApprovalNodeRecord> pendingRecords = workflowApprovalNodeRecordMapper.selectList(
                new LambdaQueryWrapper<WorkflowApprovalNodeRecord>()
                    .eq(WorkflowApprovalNodeRecord::getWorkflowId, workflowId)
                    .eq(WorkflowApprovalNodeRecord::getResult, Constants.STATE_PENDING)
            );
            if (!CollectionUtils.isEmpty(pendingRecords)) {
                for (WorkflowApprovalNodeRecord record : pendingRecords) {
                    record.setResult(Constants.STATE_TERMINATE);
                    record.setBz1(StringUtils.hasText(reason) ? "工作流终止: " + reason : "工作流已终止");
                    workflowApprovalNodeRecordMapper.updateById(record);
                }
            }
            List<WorkflowApprovalNode> pendingNodes = workflowApprovalNodeMapper.selectList(
                new LambdaQueryWrapper<WorkflowApprovalNode>()
                    .eq(WorkflowApprovalNode::getWorkflowId, workflowId)
                    .eq(WorkflowApprovalNode::getResult, Constants.STATE_PENDING)
            );
            if (!CollectionUtils.isEmpty(pendingNodes)) {
                for (WorkflowApprovalNode node : pendingNodes) {
                    node.setResult(Constants.STATE_TERMINATE);
                    node.setUpdateTime(LocalDateTime.now());
                    workflowApprovalNodeMapper.updateById(node);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("终止工作流失败: " + e.getMessage(), e);
        }
    }

    @Override
    public WorkflowDTO get(String id) {
        Workflow workflow = getById(id);
        return getWorkflowDTO(workflow);
    }

    private WorkflowDTO getWorkflowDTO(Workflow workflow) {
        if (workflow == null) return null;
        String id = workflow.getId();
        List<WorkflowNode> nodeList = workflowNodeMapper.selectList(new LambdaQueryWrapper<WorkflowNode>().eq(WorkflowNode::getWorkflowId, id).orderByAsc(WorkflowNode::getSort));
        List<NodeDTO> nodes = new ArrayList<>();
        Map<String, WorkflowCondition> conditionMap = new HashMap<>();
        List<WorkflowCondition> conditions = conditionMapper.selectList(new LambdaQueryWrapper<WorkflowCondition>().eq(WorkflowCondition::getWorkflowId, id));
        if (!CollectionUtils.isEmpty(conditions))
            conditionMap.putAll(conditions.stream().collect(Collectors.toMap(WorkflowCondition::getConditionName, Function.identity())));
        if (!CollectionUtils.isEmpty(nodeList)) {
            nodeList.forEach(node -> {
                NodeDTO nodeDTO = new NodeDTO();
                BeanUtils.copyProperties(node, nodeDTO);
                if (nodeDTO.getType().getValue().equals(NodeType.JieDian.getValue())) {
                    String nodeId = node.getId();
                    List<WorkflowNodeApprover> nodeApprovers = workflowNodeApproverMapper.selectList(new LambdaQueryWrapper<WorkflowNodeApprover>().eq(WorkflowNodeApprover::getNodeId, nodeId).eq(WorkflowNodeApprover::getWorkflowId, id));
                    if (!CollectionUtils.isEmpty(nodeApprovers)) {
                        for (WorkflowNodeApprover approver : nodeApprovers) {
                            List<WorkflowNodeForm> nodeForm = workflowNodeFormMapper.selectList(new LambdaQueryWrapper<WorkflowNodeForm>().eq(WorkflowNodeForm::getNodeId, nodeId).eq(WorkflowNodeForm::getNodeApproverId, approver.getId()).orderByAsc(WorkflowNodeForm::getSort));
                            approver.setWorkflowNodeForm(nodeForm);
                        }
                    }

                    WorkflowNodeState nodeState = workflowNodeStateMapper.selectOne(new LambdaQueryWrapper<WorkflowNodeState>().eq(WorkflowNodeState::getNodeId, nodeId).eq(WorkflowNodeState::getWorkflowId, id));
                    nodeDTO.setWorkflowNodeApprovers(nodeApprovers);
                    nodeDTO.setWorkflowNodeState(nodeState);
                }
                nodes.add(nodeDTO);
            });
        }

        Map<String, List<NodeDTO>> childNodeMap = nodes.stream().filter(n -> StringUtils.hasText(n.getParentNode())).collect(Collectors.groupingBy(WorkflowNode::getParentNode));
        List<NodeDTO> parentNodes = nodes.stream().filter(n -> !StringUtils.hasText(n.getParentNode())).collect(Collectors.toList());
        List<NodeDTO> nodeDTOS = nodes.stream().filter(n -> childNodeMap.containsKey(n.getNodeName())).toList();

        List<WorkflowConditionDetail> conditionDetails = conditionDetailMapper.selectList(new LambdaQueryWrapper<WorkflowConditionDetail>().eq(WorkflowConditionDetail::getWorkflowId, id));
        for (int i = nodeDTOS.size() - 1; i >= 0; i--) {
            NodeDTO node = nodeDTOS.get(i);
            if (!StringUtils.hasText(node.getConditionName())) continue;
            String[] strings = CommonUtil.split(node.getConditionName());
            if (strings.length <= 1) continue;
            List<NodeDTO> conditionNodes = childNodeMap.get(node.getNodeName());
            List<ConditionDTO> conditionList = new ArrayList<>();
            for (int j = 0; j < strings.length; j++) {
                String str = strings[j];
                ConditionDTO condition = new ConditionDTO();
                WorkflowCondition nodeCondition = conditionMap.get(str);
                BeanUtils.copyProperties(nodeCondition, condition);
                condition.setPriorityLevel(j + 1);
                condition.setNodeName(str);
                Optional<NodeDTO> optional = conditionNodes.stream().filter(nodeDTO -> Arrays.asList(CommonUtil.split(nodeDTO.getConditionName())).contains(str)).findAny();
                NodeDTO childNode = null;
                if (optional.isPresent()) {
                    childNode = optional.get();
                    buildConditionNode(childNode, conditionNodes);
                }
                condition.setChildNode(childNode);
                List<WorkflowConditionDetail> details = conditionDetails.stream().filter(detail -> detail.getConditionId().equals(condition.getId())).collect(Collectors.toList());
                condition.setConditionDetail(details);
                conditionList.add(condition);
            }
            node.setConditionNodes(conditionList);
        }

        int num = 0;
        int length = nodeDTOS.size();
        NodeDTO nextNode = null;
        NodeDTO preNode = null;
        while (true) {
            num++;
            if (num >= length) break;
            if (nextNode == null) nextNode = nodeDTOS.get(length - num);
            preNode = nodeDTOS.get(length - num - 1);
            if (preNode.getNodeName().equals(nextNode.getParentNode())) {
                if (Objects.equals(preNode.getType().getValue(), NodeType.JieDian.getValue()))
                    preNode.setChildNode(nextNode);
                nextNode = preNode;
            }
        }

        if (preNode != null) {
            String rootId = preNode.getId();
            parentNodes.removeIf(n -> n.getId().equals(rootId));
            parentNodes.add(preNode);
        }
        parentNodes = parentNodes.stream().sorted(Comparator.comparing(NodeDTO::getSort)).collect(Collectors.toList());

        WorkflowDTO workflowDTO = new WorkflowDTO();
        BeanUtils.copyProperties(workflow, workflowDTO);
        NodeDTO node = buildChildNode(parentNodes);
        workflowDTO.setWorkflowNodes(node);
        return workflowDTO;
    }

    @Override
    public WorkflowDTO get(String projectId, String year) {
        Workflow workflow = workflowMapper.selectOne(new LambdaQueryWrapper<Workflow>().eq(Workflow::getProjectId, projectId).eq(Workflow::getYear, year));
        return getWorkflowDTO(workflow);
    }

    @Override
    public List<Workflow> list(WorkflowParam param) {
        PageParam<Workflow, WorkflowParam> pageParam = new PageParam<>(param);
        QueryWrapper<Workflow> orderWrapper = pageParam.getOrderWrapper();
        return workflowMapper.selectList(orderWrapper);
    }

    @Override
    public Workflow getByNodeId(String nodeId) {
        MyMPJLambdaWrapper<Workflow, WorkflowParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.exists("select 1 from syt_workflow_node t1 where t1.workflow_id = " + wrapper.getAlias() + ".id and t1.id = '" + nodeId + "'");
        return workflowMapper.selectJoinOne(Workflow.class, wrapper);
    }

    private List<NodeDTO> buildConditionNode(NodeDTO childNode, List<NodeDTO> nodes) {
        List<NodeDTO> nodeDTOS = nodes.stream().filter(node -> childNode.getParentNode().equals(node.getParentNode()) &&
//                Arrays.stream(CommonUtil.split(node.getConditionName())).anyMatch(str ->
//                        Arrays.asList(CommonUtil.split(childNode.getConditionName())).contains(str) &&
                Objects.equals(childNode.getConditionName(), node.getConditionName()) && node.getSort() > childNode.getSort()).collect(Collectors.toList());
        NodeDTO node = buildChildNode(nodeDTOS);
        childNode.setChildNode(node);
        return nodeDTOS;
    }

    private NodeDTO buildChildNode(List<NodeDTO> nodes) {
        if (CollectionUtils.isEmpty(nodes)) return null;
        if (nodes.size() == 1) return nodes.get(0);
        int index = 0;
        int length = nodes.size();
        NodeDTO nextNode = null;
        NodeDTO node = null;
        while (true) {
            index++;
            if (index >= length) break;
            if (nextNode == null) nextNode = nodes.get(length - index);
            node = nodes.get(length - index - 1);
            NodeDTO childNode = node.getChildNode();
            if (!Objects.isNull(childNode)) {
                childNode.setChildNode(nextNode);
            } else {
                node.setChildNode(nextNode);
            }
            nextNode = node;
        }
        return node;
    }

    @Override
    public <T extends BaseApprovalNode, R extends BaseApplicationInfo> String createApprovalNode(Class<T> tClass, R info, WorkflowParam workflowParam, UserInfo userInfo) {
        SqlSession sqlSession = null;
        try {
            SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
            sqlSession = sqlSessionFactory.openSession();
            BaseMapper<T> approvalNodeMapper = SqlHelper.getMapper(tClass, sqlSession);
            List<T> approvalNodes = getApprovalNodes(info.getId(), null, null, approvalNodeMapper);
            if (!CollectionUtils.isEmpty(approvalNodes)) {
                T approvalNode = approvalNodes.get(0);
                if (Objects.equals(ConstantsWorkflow.STATE_RETURN, approvalNode.getResult())) {
                    approvalNode.setResult(ConstantsWorkflow.STATE_PENDING);
                    approvalNodeMapper.updateById(approvalNode);
                }
                return approvalNode.getWorkflowId();
            }
            Workflow workflow = CommonUtil.listGetOne(list(workflowParam));
            if (workflow == null) return null;
            List<WorkflowNode> nodeList = workflowNodeMapper.selectList(new LambdaQueryWrapper<WorkflowNode>().eq(WorkflowNode::getType, NodeType.JieDian).eq(WorkflowNode::getWorkflowId, workflow.getId()).orderByAsc(WorkflowNode::getSort));
            if (CollectionUtils.isEmpty(nodeList)) return null;
            if (Objects.isNull(userInfo)) userInfo = userInfoService.get(info.getXgh());
            WorkflowNode nextNode = WorkflowUtil.nextNode(info.getXgh(), null, nodeList, info, userInfo);
            if (nextNode == null) return null;
            T approvalNode = WorkflowUtil.getApprovalNode(tClass, nextNode, info.getId(), true);
            approvalNodeMapper.insert(approvalNode);
            return workflow.getId();
            // 发送审批通知
        } finally {
            if (!Objects.isNull(sqlSession))
                sqlSession.close();
        }
    }


    @SuppressWarnings("unchecked")
    @Override
    public <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord, R extends BaseApplicationInfo> ApprovalInfo<T, U> approve(Class<T> tClass, U record, R info, WorkflowParam param, UserInfo userInfo) {
        SqlSession sqlSession = null;
        try {
            SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
            sqlSession = sqlSessionFactory.openSession();
            SysAccount account = SecurityUtil.getAccount();
            String username = account.getUsername();
            String roleId = account.getRole().getId();
            String usernameAndRoleStr = getApproverIds(account);
            List<WorkflowNodeApprover> list = workflowNodeApproverMapper.nodeApproverList(param, usernameAndRoleStr);
            AssertUtil.isTrue(!CollectionUtils.isEmpty(list), "无审核权限");
            BaseMapper<T> approvalNodeMapper = SqlHelper.getMapper(tClass, sqlSession);
            BaseMapper<U> approvalNodeRecordMapper = (BaseMapper<U>) SqlHelper.getMapper(record.getClass(), sqlSession);
            String workflowId = list.get(0).getWorkflowId();
            List<T> approvalNodeList = getApprovalNodes(info.getId(), workflowId, null, approvalNodeMapper);
            List<WorkflowNode> nodeList = workflowNodeMapper.selectList(new LambdaQueryWrapper<WorkflowNode>()
                    .eq(WorkflowNode::getWorkflowId, workflowId).eq(WorkflowNode::getType, NodeType.JieDian)
                    .orderByAsc(WorkflowNode::getSort));
            T currentApprovalNode = approvalNodeGetAndCheck(record, approvalNodeList, list, nodeList, username, approvalNodeRecordMapper);
            if (Objects.isNull(userInfo)) userInfo = userInfoService.get(info.getXgh());
            ApprovalData<R, U> approvalData = new ApprovalData<>(info, record, userInfo);
            workflowNodeFormEventSubscription.notify(approvalData);

            WorkflowNodeApprover nodeApprover = WorkflowUtil.getNodeApprover(list, currentApprovalNode);
            currentApprovalNode.setUpdateTime(LocalDateTime.now());
            currentApprovalNode.setResult(record.getResult());
            WorkflowNode nextNode = WorkflowUtil.nextNode(info.getXgh(), currentApprovalNode.getSort(), nodeList, info, userInfo);
            if (Objects.isNull(nextNode)) {
                currentApprovalNode.setEndNode(JudgeMark.YES);
                currentApprovalNode.setNextNodeResult(null);
            } else {
                currentApprovalNode.setEndNode(JudgeMark.NO);
                List<WorkflowNodeApprover> nodeApprovers = workflowNodeApproverMapper.selectList(new LambdaQueryWrapper<WorkflowNodeApprover>()
                        .eq(WorkflowNodeApprover::getNodeId, nextNode.getId())
                        .eq(WorkflowNodeApprover::getWorkflowId, workflowId));
                approvalData.setNextNodeApprovers(nodeApprovers);
            }

            ReviewResult reviewResult = ReviewResult.ShenPiZhong;
            T preApprovalNode = WorkflowUtil.preApprovalNode(currentApprovalNode, approvalNodeList);
            boolean REMOVE_NEXT_APPROVALNODE = false;
            String nextApprovableNodeId = null;
            if (Objects.equals(ConstantsWorkflow.STATE_RETURN, record.getResult())) {       // 退回操作
                REMOVE_NEXT_APPROVALNODE = true;
                boolean BACK_APPLICANT = true;
                if (Objects.equals(ReturnWay.PRE_NODE, record.getReturnWay())) {
                    if (!Objects.isNull(preApprovalNode)) {
                        preApprovalNode.setResult(ConstantsWorkflow.STATE_PENDING);
                        preApprovalNode.setNextNodeResult(ConstantsWorkflow.STATE_RETURN);
                        preApprovalNode.setStage(ReviewStage.FuShen);
                        approvalNodeMapper.updateById(preApprovalNode);
                        BACK_APPLICANT = false;
                    }
                } else if (Objects.equals(ReturnWay.APPLICANT, record.getReturnWay())) {
                    Integer index = WorkflowUtil.currentApprovalNodeIndex(currentApprovalNode, approvalNodeList);
                    for (int i = index - 1; i >= 0; i--) {
                        T node = approvalNodeList.get(i);
                        String nextNodeResult = (i == index - 1) ? ConstantsWorkflow.STATE_RETURN : ConstantsWorkflow.STATE_PENDING;
                        node.setResult(ConstantsWorkflow.STATE_PENDING);
                        node.setPreNodeResult(ConstantsWorkflow.STATE_PENDING);
                        node.setNextNodeResult(nextNodeResult);
                        node.setStage(ReviewStage.FuShen);
                        approvalNodeMapper.updateById(node);
                    }
                }

                currentApprovalNode.setPreNodeResult(!Objects.isNull(preApprovalNode) ? ConstantsWorkflow.STATE_PENDING : null);
                currentApprovalNode.setStage(ReviewStage.FuShen);
                currentApprovalNode.setReturnWay(record.getReturnWay());
                approvalNodeMapper.updateById(currentApprovalNode);
                if (BACK_APPLICANT) reviewResult = ReviewResult.TuiHui;
            } else {
                if (Objects.equals(ConstantsWorkflow.STATE_PASS, record.getResult())) {
                    boolean updateApprovalNode = checkOtherApprover(info.getId(), currentApprovalNode, usernameAndRoleStr, approvalNodeRecordMapper);
                    if (updateApprovalNode) {
                        if (!currentApprovalNode.getEndNode().getMark()) {
                            T nextApprovalNode = WorkflowUtil.getApprovalNode(tClass, nextNode, info.getId(), false);
                            nextApprovalNode.setPreNodeResult(currentApprovalNode.getResult());
                            T object = CommonUtil.listGetOne(getApprovalNodes(info.getId(), workflowId, nextNode.getId(), approvalNodeMapper));
                            if (Objects.isNull(object)) {
                                approvalNodeMapper.insert(nextApprovalNode);
                            } else {
                                object.setUpdateTime(null);
                                object.setPreNodeResult(currentApprovalNode.getResult());
                                object.setResult(ConstantsWorkflow.STATE_PENDING);
                                approvalNodeMapper.updateById(object);
                                currentApprovalNode.setNextNodeResult(nextApprovalNode.getResult());
                            }
                            if (canApproveNextNode(account, nextNode.getId()))
                                nextApprovableNodeId = nextNode.getId();
                        }

                        if (currentApprovalNode.getEndNode().getMark()) reviewResult = ReviewResult.TongGuo;
                    }
                } else if (Objects.equals(ConstantsWorkflow.STATE_TERMINATE, record.getResult())) {
                    REMOVE_NEXT_APPROVALNODE = true;
                    reviewResult = ReviewResult.BuTongGuo;
                }

                approvalNodeMapper.updateById(currentApprovalNode);
                if (!Objects.isNull(preApprovalNode)) {
                    preApprovalNode.setNextNodeResult(currentApprovalNode.getResult());
                    approvalNodeMapper.updateById(preApprovalNode);
                }
            }

            if (REMOVE_NEXT_APPROVALNODE) {
                T nextApprovalNode = WorkflowUtil.nextApprovalNode(currentApprovalNode, approvalNodeList);
                if (!Objects.isNull(nextApprovalNode) && Objects.equals(ConstantsWorkflow.STATE_PENDING, nextApprovalNode.getResult()) && Objects.equals(ReviewStage.ChuShen, nextApprovalNode.getStage()))
                    approvalNodeMapper.deleteById(nextApprovalNode.getId());
            }

            WorkflowUtil.uploadApprovalFile(record);

            record.setId(null);
            record.setNodeApproverId(nodeApprover.getId());
            record.setApproverId(nodeApprover.getApproverId());
            record.setCreateTime(LocalDateTime.now());
            record.setRoleId(roleId);
            record.setUsername(username);
            record.setRealName(account.getRealName());
            record.setWorkflowId(workflowId);
            record.setStage(currentApprovalNode.getStage());
            approvalNodeRecordMapper.insert(record);
            workflowNodeEventSubscription.notify(approvalData);               // 触发节点事件
            return new ApprovalInfo<T, U>(currentApprovalNode, record, reviewResult, nextApprovableNodeId);
        } finally {
            if (!Objects.isNull(sqlSession))
                sqlSession.close();
        }
    }

    private boolean canApproveNextNode(SysAccount account, String nextNodeId) {
        Long count = workflowNodeApproverMapper.selectCount(new LambdaQueryWrapper<WorkflowNodeApprover>()
                .in(WorkflowNodeApprover::getApproverId, (Object[]) CommonUtil.split(getApproverIds(account)))
                .eq(WorkflowNodeApprover::getNodeId, nextNodeId));
        return count > 0;
    }

    private String getApproverIds(SysAccount account) {
        List<String> approverIds = new ArrayList<>();
        approverIds.add(account.getUsername());
        approverIds.addAll(accountRoleMapper.selectObjs(new LambdaQueryWrapper<SysAccountRole>()
                .select(SysAccountRole::getRoleId).eq(SysAccountRole::getAccountId, account.getId())));
        return CommonUtil.appendComma(approverIds.toArray(new String[]{}));
    }

    private <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> T approvalNodeGetAndCheck(U record, List<T> approvalNodeList, List<WorkflowNodeApprover> list, List<WorkflowNode> nodeList, String username, BaseMapper<U> approvalNodeRecordMapper) {
        String message = "操作失败,当前审核节点未在本用户";
        AssertUtil.isTrue(!CollectionUtils.isEmpty(approvalNodeList), message);
        T currentApprovalNode;
        if (StringUtils.hasText(record.getNodeId())) {
            Optional<T> optional = approvalNodeList.stream().filter(node -> Objects.equals(node.getNodeId(), record.getNodeId())).findAny();
            currentApprovalNode = optional.orElse(null);
        } else {
            currentApprovalNode = WorkflowUtil.getApprovalNode(list, approvalNodeList);
            record.setNodeId(currentApprovalNode.getNodeId());
        }

        if (currentApprovalNode == null || Objects.equals(ConstantsWorkflow.STATE_PASS, currentApprovalNode.getNextNodeResult())
                || Objects.equals(ConstantsWorkflow.STATE_REJECT, currentApprovalNode.getNextNodeResult())
                || Objects.equals(ConstantsWorkflow.STATE_TERMINATE, currentApprovalNode.getNextNodeResult())
                || Objects.equals(ConstantsWorkflow.STATE_RETURN, currentApprovalNode.getResult()))
            AssertUtil.throwMessage(message);
        WorkflowNode node = WorkflowUtil.node(currentApprovalNode.getNodeId(), nodeList);
        AssertUtil.isTrue(node.approvalTimeIsCorrect(), "操作失败,未在审核时间范围内");
        if (Objects.equals(ReviewType.huiQian, node.getReviewType()) && Objects.equals(ConstantsWorkflow.STATE_TERMINATE, currentApprovalNode.getResult())) {
            U nodeRecord = lastApprovalRecord(record.getApplicationId(), currentApprovalNode.getNodeId(), approvalNodeRecordMapper);
            if (!Objects.isNull(nodeRecord) && !Objects.equals(username, nodeRecord.getUsername()))
                AssertUtil.throwMessage(message);
        }

        T nextApprovalNode = WorkflowUtil.nextApprovalNode(currentApprovalNode, approvalNodeList);
        if (!Objects.isNull(nextApprovalNode)) {
            WorkflowNode nextNode = WorkflowUtil.node(nextApprovalNode.getNodeId(), nodeList);
            if (Objects.equals(ReviewType.huiQian, nextNode.getReviewType()) && Objects.equals(ConstantsWorkflow.STATE_PENDING, nextApprovalNode.getResult())) {
                U nodeRecord = lastApprovalRecord(record.getApplicationId(), nextApprovalNode.getNodeId(), approvalNodeRecordMapper);
                if (!Objects.isNull(nodeRecord) && Objects.equals(ConstantsWorkflow.STATE_PASS, nodeRecord.getResult()))
                    AssertUtil.throwMessage(message);
            }
        }
        return currentApprovalNode;
    }

    private <U extends BaseApprovalNodeRecord> U lastApprovalRecord(String applicationId, String nodeId, BaseMapper<U> approvalNodeRecordMapper) {
        Page<U> page = new Page<>(1, 1);
        QueryWrapper<U> queryWrapper = approvalNodeRecordQueryWrapper(applicationId, Collections.singleton(nodeId), null, null);
        page = approvalNodeRecordMapper.selectPage(page, queryWrapper);
        return (!Objects.isNull(page) && !CollectionUtils.isEmpty(page.getRecords())) ? page.getRecords().get(0) : null;
    }

    private <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> boolean checkOtherApprover(String applicationId, T currentApprovalNode, String usernameAndRoleStr, BaseMapper<U> approvalNodeRecordMapper) {
        boolean updateApprovalNode = true;
        if (currentApprovalNode.getReviewType() != null) {
            if (Objects.equals(ReviewType.huiQian, currentApprovalNode.getReviewType())) {
                List<WorkflowNodeApprover> otherApprovers = workflowNodeApproverMapper.selectList(new LambdaQueryWrapper<WorkflowNodeApprover>()
                        .eq(WorkflowNodeApprover::getNodeId, currentApprovalNode.getNodeId())
                        .notIn(WorkflowNodeApprover::getApproverId, CommonUtil.split(usernameAndRoleStr)));
                if (!CollectionUtils.isEmpty(otherApprovers)) {
                    List<String> resultList = new ArrayList<>();
                    for (WorkflowNodeApprover approver : otherApprovers) {
                        QueryWrapper<U> queryWrapper = approvalNodeRecordQueryWrapper(applicationId, Collections.singleton(currentApprovalNode.getNodeId()), approver.getApproverId(), null);
                        List<U> records = approvalNodeRecordMapper.selectList(queryWrapper);
                        if (!CollectionUtils.isEmpty(records)) resultList.add(records.get(0).getResult());
                    }

                    if (resultList.size() == otherApprovers.size() && !resultList.contains(ConstantsWorkflow.STATE_TERMINATE) && !resultList.contains(ConstantsWorkflow.STATE_REJECT) && !resultList.contains(ConstantsWorkflow.STATE_RETURN)) {
                        currentApprovalNode.setResult(ConstantsWorkflow.STATE_PASS);
                    } else {
                        currentApprovalNode.setResult(ConstantsWorkflow.STATE_PENDING);
                        updateApprovalNode = false;
                    }
                }
            }
        }
        return updateApprovalNode;
    }

    private static <T extends BaseApprovalNode> List<T> getApprovalNodes(@NotEmpty String applicationId, String workflowId, String nodeId, BaseMapper<T> approvalNodeMapper) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("APPLICATION_ID", applicationId);
        if (StringUtils.hasText(workflowId)) queryWrapper.eq("WORKFLOW_ID", workflowId);
        if (StringUtils.hasText(nodeId)) queryWrapper.eq("NODE_ID", nodeId);
        queryWrapper.orderByAsc("SORT");
        return approvalNodeMapper.selectList(queryWrapper);
    }

    private static <U extends BaseApprovalNodeRecord> QueryWrapper<U> approvalNodeRecordQueryWrapper(@NotEmpty String applicationId, @NotNull Collection<String> nodeId, String approverId, String username, Object... result) {
        QueryWrapper<U> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("APPLICATION_ID", applicationId);
        queryWrapper.in("NODE_ID", nodeId.toArray());
        if (StringUtils.hasText(approverId)) queryWrapper.in("APPROVER_ID", (Object[]) CommonUtil.split(approverId));
        if (StringUtils.hasText(username)) queryWrapper.eq("USERNAME", username);
        if (!Objects.isNull(result) && result.length > 0) queryWrapper.in("RESULT", result);
        queryWrapper.orderByDesc("CREATE_TIME");
        return queryWrapper;
    }

    @Override
    public <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> String checkApplicationInfoIsCanEdit(Class<T> tClass, Class<U> recordClass, String applicationId) {
        SqlSession sqlSession = null;
        try {
            SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
            sqlSession = sqlSessionFactory.openSession();
            BaseMapper<T> approvalNodeMapper = SqlHelper.getMapper(tClass, sqlSession);
            BaseMapper<U> approvalNodeRecordMapper = SqlHelper.getMapper(recordClass, sqlSession);
            String[] arrays = {ConstantsWorkflow.STATE_PASS, ConstantsWorkflow.STATE_TERMINATE};
            List<String> list = Arrays.asList(arrays);
            List<T> approvalNodes = getApprovalNodes(applicationId, null, null, approvalNodeMapper);
            if (CollectionUtils.isEmpty(approvalNodes))
                return null;
            T approvalNode = approvalNodes.get(0);
            if (list.contains(approvalNode.getResult())) AssertUtil.throwMessage("操作失败, 申请信息已审核");
            if (Objects.equals(ConstantsWorkflow.STATE_PENDING, approvalNode.getResult())) {
                WorkflowNode node = workflowNodeMapper.selectById(approvalNode.getNodeId());
                if (node.getReviewType() != null && Objects.equals(ReviewType.huiQian, node.getReviewType())) {
                    Long count = approvalNodeRecordMapper.selectCount(approvalNodeRecordQueryWrapper(applicationId, Collections.singleton(node.getId()), null, null, (Object[]) arrays));
                    AssertUtil.isTrue(count == 0, "操作失败, 申请信息已审核");
                }
            }
            return approvalNode.getWorkflowId();
        } finally {
            if (!Objects.isNull(sqlSession))
                sqlSession.close();
        }
    }

    @Override
    public <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> List<U> approvalNodeRecordList(Class<T> tClass, Class<U> recordClass, WorkflowParam param, String applicationId, String nodeId) {
        SqlSession sqlSession = null;
        try {
            String username = SecurityUtil.getUsername();
            String roleId = SecurityUtil.getRoleId();
            String usernameAndRoleStr = CommonUtil.strJoin(username, roleId);
            SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
            sqlSession = sqlSessionFactory.openSession();
            BaseMapper<U> approvalNodeRecordMapper = SqlHelper.getMapper(recordClass, sqlSession);
            if (!StringUtils.hasText(nodeId)) {
                List<WorkflowNodeApprover> list = workflowNodeApproverMapper.nodeApproverList(param, usernameAndRoleStr);
                if (CollectionUtils.isEmpty(list)) return null;
                BaseMapper<T> approvalNodeMapper = SqlHelper.getMapper(tClass, sqlSession);
                List<T> approvalNodeList = getApprovalNodes(applicationId, list.get(0).getWorkflowId(), null, approvalNodeMapper);
                T approvalNode = WorkflowUtil.getApprovalNode(list, approvalNodeList);
                if (Objects.isNull(approvalNode)) return null;
                nodeId = approvalNode.getNodeId();
            }
            QueryWrapper<U> wrapper = approvalNodeRecordQueryWrapper(applicationId, Collections.singleton(nodeId), usernameAndRoleStr, username);
            return approvalNodeRecordMapper.selectList(wrapper);
        } finally {
            if (!Objects.isNull(sqlSession))
                sqlSession.close();
        }
    }

    @Override
    public <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> List<ApprovalNodeInfo<T, U>> approvalNodeInfo(@NotNull Class<T> tClass, @NotNull Class<U> recordClass, @NotEmpty String applicationId, String nodeId) {
        SqlSession sqlSession = null;
        try {
            SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
            sqlSession = sqlSessionFactory.openSession();
            BaseMapper<T> approvalNodeMapper = SqlHelper.getMapper(tClass, sqlSession);
            BaseMapper<U> approvalNodeRecordMapper = SqlHelper.getMapper(recordClass, sqlSession);
            List<T> approvalNodes = getApprovalNodes(applicationId, null, nodeId, approvalNodeMapper);
            List<String> nodeIds = approvalNodes.stream().map(T::getNodeId).toList();
            List<U> records = approvalNodeRecordMapper.selectList(approvalNodeRecordQueryWrapper(applicationId, nodeIds, null, null));

            Map<String, List<U>> recordMap = new HashMap<>();
            Map<String, List<WorkflowNodeForm>> nodeFormMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(records)) {
                recordMap = records.stream().collect(Collectors.groupingBy(U::getNodeId));
                List<String> nodeApproverIds = records.stream().map(U::getNodeApproverId).toList();
                List<WorkflowNodeForm> nodeForms = workflowNodeFormMapper.selectList(new LambdaQueryWrapper<WorkflowNodeForm>()
                        .in(WorkflowNodeForm::getNodeId, nodeIds)
                        .in(WorkflowNodeForm::getNodeApproverId, nodeApproverIds));
                nodeFormMap = nodeForms.stream().collect(Collectors.groupingBy(WorkflowNodeForm::getNodeId));
            }

            List<ApprovalNodeInfo<T, U>> list = new ArrayList<>();
            for (T approvalNode : approvalNodes) {
                ApprovalNodeInfo<T, U> dto = new ApprovalNodeInfo<>();
                dto.setApprovalNode(approvalNode);
                dto.setApprovalNodeRecords(recordMap.get(approvalNode.getNodeId()));
                dto.setNodeForms(nodeFormMap.get(approvalNode.getNodeId()));
                list.add(dto);
            }
            return list;
        } finally {
            if (!Objects.isNull(sqlSession))
                sqlSession.close();
        }
    }

    @Override
    public <T extends BaseApprovalNode> List<WorkflowNodeStateDTO> nodeCustomState(Class<T> tClass, WorkflowParam param, String nodeId, String applicationId) {
        SqlSession sqlSession = null;
        try {
            List<WorkflowNode> nodes = workflowNodeMapper.nodeListByApprover(param,
                    CommonUtil.appendComma(SecurityUtil.getUsername(), SecurityUtil.getRoleId()));
            if (CollectionUtils.isEmpty(nodes))
                return null;
            List<String> nodeIds = new ArrayList<>(nodes.stream().map(WorkflowNode::getId).toList());
            if (StringUtils.hasText(nodeId))
                nodeIds.removeIf(nid -> !Objects.equals(nid, nodeId));
            if (StringUtils.hasText(applicationId)) {
                SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
                sqlSession = sqlSessionFactory.openSession();
                BaseMapper<T> approvalNodeMapper = SqlHelper.getMapper(tClass, sqlSession);
                List<T> approvalNodeList = getApprovalNodes(applicationId, null, null, approvalNodeMapper);
                T approvalNode = WorkflowUtil.getApprovalNode(nodes, approvalNodeList);
                nodeIds.removeIf(nid -> !Objects.equals(nid, approvalNode.getNodeId()));
            }

            List<WorkflowNodeStateDTO> nodeStateDTOS = new ArrayList<>();
            List<WorkflowNodeState> list = workflowNodeStateMapper.selectList(new LambdaQueryWrapper<WorkflowNodeState>()
                    .in(WorkflowNodeState::getNodeId, nodeIds));
            for (WorkflowNodeState nodeState : list) {
                WorkflowNodeStateDTO nodeStateDTO = new WorkflowNodeStateDTO();
                BeanUtils.copyProperties(nodeState, nodeStateDTO);
                Optional<WorkflowNode> optional = nodes.stream().filter(node -> Objects.equals(node.getId(), nodeState.getNodeId())).findAny();
                WorkflowNode node = optional.orElse(null);
                if (node != null) {
                    nodeStateDTO.setNodeName(node.getNodeName());
                    nodeStateDTO.setApprovalTimeIsCorrect(node.approvalTimeIsCorrect());
                }
                nodeStateDTOS.add(nodeStateDTO);
            }
            return nodeStateDTOS;
        } finally {
            if (!Objects.isNull(sqlSession))
                sqlSession.close();
        }
    }

    /**
     * 检查申请信息是否可以删除
     *
     * @param tClass        审批节点类
     * @param recordClass   审批记录类
     * @param applicationId 申请ID
     * @param approverId    审批人ID (格式: projectId/sysModelCode,year)
     * @return 工作流ID
     */
    @Override
    public <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> String checkApplicationInfoIsCanDelete(Class<T> tClass, Class<U> recordClass,
                                                                                                                 String projectIdOrSysModelCode, String year, String applicationId, String approverId) {
        SqlSession sqlSession = null;
        try {
            SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
            sqlSession = sqlSessionFactory.openSession();
            BaseMapper<T> approvalNodeMapper = SqlHelper.getMapper(tClass, sqlSession);
            List<T> approvalNodes = getApprovalNodes(applicationId, null, null, approvalNodeMapper);
            if (CollectionUtils.isEmpty(approvalNodes)) {
                AssertUtil.throwMessage("未找到相关审批流程信息");
            }
            T firstNode = approvalNodes.get(0);
            String workflowId = firstNode.getWorkflowId();
            List<WorkflowNode> currentUserNodes = workflowNodeMapper.nodeListByFormInfoAndApprover(projectIdOrSysModelCode, year, approverId);

            if (CollectionUtils.isEmpty(currentUserNodes)) {
                AssertUtil.throwMessage("当前用户无权限删除该申请");
            }
            List<WorkflowNode> allNodes = workflowNodeMapper.selectList(new LambdaQueryWrapper<WorkflowNode>().eq(WorkflowNode::getWorkflowId, workflowId).orderByAsc(WorkflowNode::getSort));
            List<WorkflowConditionDetail> conditionDetails = conditionDetailMapper.selectList(new LambdaQueryWrapper<WorkflowConditionDetail>().eq(WorkflowConditionDetail::getWorkflowId, workflowId));
            Map<String, List<WorkflowConditionDetail>> conditionMap = CollectionUtils.isEmpty(conditionDetails) ? new HashMap<>() : conditionDetails.stream().collect(Collectors.groupingBy(WorkflowConditionDetail::getConditionId));
            List<WorkflowNode> matchedNodes = new ArrayList<>();
            for (WorkflowNode node : allNodes) {
                if (WorkflowUtil.checkConditionIsMatch(node, firstNode, null, conditionMap)) {
                    matchedNodes.add(node);
                }
            }

            Set<String> matchedNodeIds = matchedNodes.stream()
                    .map(WorkflowNode::getId)
                    .collect(Collectors.toSet());
            currentUserNodes.removeIf(node -> !matchedNodeIds.contains(node.getId()));
            boolean canDelete = false;
            WorkflowNode lastMatchedNode = matchedNodes.get(matchedNodes.size() - 1);
            if (currentUserNodes.stream().anyMatch(node -> node.getId().equals(lastMatchedNode.getId()))) {
                canDelete = true;
            } else {
                T currentNode = approvalNodes.stream()
                        .filter(node -> currentUserNodes.stream()
                                .anyMatch(n -> n.getId().equals(node.getNodeId())))
                        .findFirst()
                        .orElse(null);
                if (currentNode != null) {
                    int currentIndex = approvalNodes.indexOf(currentNode);
                    if (currentIndex < approvalNodes.size() - 1) {
                        T nextNode = approvalNodes.get(currentIndex + 1);
                        canDelete = Objects.equals(ConstantsWorkflow.STATE_PENDING, nextNode.getResult());
                    }
                }
            }
            if (!canDelete) {
                AssertUtil.throwMessage("操作失败，下一级已审批，无法删除");
            }
            return workflowId;
        } finally {
            if (sqlSession != null) {
                sqlSession.close();
            }
        }
    }

    @Override
    public <T extends BaseApplicationInfo, U extends BaseParam> void getApprovalListSubQueryWrapper(MyMPJLambdaWrapper<T, U> wrapper, WorkflowNodeApproverMapper nodeApproverMapper,
                                                                                                    String projectIdOrSysModelCode, String year, String nodeId, String result) {
        SqlSession sqlSession = null;
        try {
            SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
            sqlSession = sqlSessionFactory.openSession();
            if (org.apache.commons.lang3.StringUtils.isEmpty(nodeId)) {
                String s = CommonUtil.appendComma(SecurityUtil.getUsername(), SecurityUtil.getRoleId());
                List<WorkflowNodeApprover> list = nodeApproverMapper.nodeListByFormInfoAndApprover(projectIdOrSysModelCode, year, s);
                if (!CollectionUtils.isEmpty(list)) {
                    List<String> strings = list.stream().map(WorkflowNodeApprover::getNodeId).toList();
                    nodeId = CommonUtil.str2SqlIn(strings.toArray(new String[]{}));
                }
            } else {
                nodeId = CommonUtil.str2SqlIn(nodeId);
            }

            StringBuilder existsSql = new StringBuilder("SELECT 1 FROM SYT_WORKFLOW_APPROVAL_NODE WHERE " +
                    "NODE_ID IN (" + nodeId + ") AND " + wrapper.getAlias() + ".ID = APPLICATION_ID");
            if (org.apache.commons.lang3.StringUtils.isNoneBlank(result))
                existsSql.append(" AND RESULT = ").append(CommonUtil.str2SqlIn(result));
            wrapper.exists(existsSql.toString());


        } finally {
            if (sqlSession != null) {
                sqlSession.close();
            }
        }
    }
}
