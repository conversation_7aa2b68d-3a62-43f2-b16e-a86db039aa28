[2m2025-08-07 08:31:34.442[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-07 08:31:34.688[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Starting AdminApplication using Java 17.0.15 with PID 3856 (/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-xuegong-5)
[2m2025-08-07 08:31:34.689[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-07 08:31:34.689[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-07 08:31:36.257[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-07 08:31:36.259[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 08:31:36.578[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: jakarta.persistence.Entity, jakarta.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository
[2m2025-08-07 08:31:36.728[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 449 ms. Found 27 JPA repository interfaces.
[2m2025-08-07 08:31:36.771[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-07 08:31:36.771[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 08:31:36.808[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.808[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitEventTypeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.811[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.811[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.811[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.813[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.813[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.813[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.841[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 68 ms. Found 1 MongoDB repository interface.
[2m2025-08-07 08:31:36.856[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-07 08:31:36.857[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitEventTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 08:31:37.352[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
[2m2025-08-07 08:31:37.905[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8082 (http)
[2m2025-08-07 08:31:37.925[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-8082"]
[2m2025-08-07 08:31:37.926[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-07 08:31:37.926[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-07 08:31:37.987[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-07 08:31:37.987[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3239 ms
[2m2025-08-07 08:31:38.139[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.6"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@6587c92f, com.mongodb.Jep395RecordCodecProvider@537979aa, com.mongodb.KotlinCodecProvider@5ac25491]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-07 08:31:38.258[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=66108208, minRoundTripTimeNanos=0}
[2m2025-08-07 08:31:38.496[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-07 08:31:38.715[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-07 08:31:38.720[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanythadmin.common.core.mybatisplus.interceptor.DecryptInterceptor@3e13a74'
[2m2025-08-07 08:31:38.720[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@71e064b2'
[2m2025-08-07 08:31:38.720[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@20bd4fd2'
[2m2025-08-07 08:31:38.918[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/EmailRecordMapper.xml]'
[2m2025-08-07 08:31:38.973[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-07 08:31:39.004[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-07 08:31:39.039[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-07 08:31:39.061[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-07 08:31:39.080[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysJwtMapper.xml]'
[2m2025-08-07 08:31:39.127[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-07 08:31:39.155[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysModuleSetupMapper.xml]'
[2m2025-08-07 08:31:39.183[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-07 08:31:39.203[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-07 08:31:39.225[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-07 08:31:39.281[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-07 08:31:39.299[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserOrgMapMapper.xml]'
[2m2025-08-07 08:31:39.318[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinAddressMapper.xml]'
[2m2025-08-07 08:31:39.342[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemAddressMapper.xml]'
[2m2025-08-07 08:31:39.365[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemMapper.xml]'
[2m2025-08-07 08:31:39.391[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinRecordMapper.xml]'
[2m2025-08-07 08:31:39.414[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-07 08:31:39.437[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-07 08:31:39.457[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-07 08:31:39.473[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeMzbMapper.xml]'
[2m2025-08-07 08:31:39.489[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeNjbMapper.xml]'
[2m2025-08-07 08:31:39.505[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodePyccbMapper.xml]'
[2m2025-08-07 08:31:39.526[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-07 08:31:39.548[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXqbMapper.xml]'
[2m2025-08-07 08:31:39.564[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXsztMapper.xml]'
[2m2025-08-07 08:31:39.579[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-07 08:31:39.593[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZzmmbMapper.xml]'
[2m2025-08-07 08:31:39.610[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/ScoreSourceConfigMapper.xml]'
[2m2025-08-07 08:31:39.630[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionFieldMapper.xml]'
[2m2025-08-07 08:31:39.646[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionMapper.xml]'
[2m2025-08-07 08:31:39.665[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldEduLevelMapper.xml]'
[2m2025-08-07 08:31:39.684[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldHideRoleMapper.xml]'
[2m2025-08-07 08:31:39.704[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldLinkMapper.xml]'
[2m2025-08-07 08:31:39.725[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldMapper.xml]'
[2m2025-08-07 08:31:39.739[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupEduLevelMapper.xml]'
[2m2025-08-07 08:31:39.753[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupHideRoleMapper.xml]'
[2m2025-08-07 08:31:39.770[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupMapper.xml]'
[2m2025-08-07 08:31:39.784[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/ListGroupConfigMapper.xml]'
[2m2025-08-07 08:31:39.798[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/SelectControlApiMapper.xml]'
[2m2025-08-07 08:31:39.832[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationInfoMapper.xml]'
[2m2025-08-07 08:31:39.854[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationReferenceInfoMapper.xml]'
[2m2025-08-07 08:31:39.879[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeMapper.xml]'
[2m2025-08-07 08:31:39.900[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeRecordMapper.xml]'
[2m2025-08-07 08:31:39.917[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigMapper.xml]'
[2m2025-08-07 08:31:39.932[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigScopeMapper.xml]'
[2m2025-08-07 08:31:39.949[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailMapper.xml]'
[2m2025-08-07 08:31:39.963[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailScopeMapper.xml]'
[2m2025-08-07 08:31:39.980[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemMapper.xml]'
[2m2025-08-07 08:31:39.996[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluatePeerReviewRecordMapper.xml]'
[2m2025-08-07 08:31:40.011[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateReviewScoreMapper.xml]'
[2m2025-08-07 08:31:40.032[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreConfirmRecordMapper.xml]'
[2m2025-08-07 08:31:40.056[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreMapper.xml]'
[2m2025-08-07 08:31:40.084[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerInfoMapper.xml]'
[2m2025-08-07 08:31:40.102[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerMapper.xml]'
[2m2025-08-07 08:31:40.118[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamClassMapper.xml]'
[2m2025-08-07 08:31:40.137[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamMapper.xml]'
[2m2025-08-07 08:31:40.152[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperClassMapper.xml]'
[2m2025-08-07 08:31:40.155[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperContentMapper.xml]'
[2m2025-08-07 08:31:40.173[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperMapper.xml]'
[2m2025-08-07 08:31:40.187[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQgroupMapper.xml]'
[2m2025-08-07 08:31:40.203[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsMapper.xml]'
[2m2025-08-07 08:31:40.224[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsOptionsMapper.xml]'
[2m2025-08-07 08:31:40.238[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsRuleMapper.xml]'
[2m2025-08-07 08:31:40.252[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsClassMapper.xml]'
[2m2025-08-07 08:31:40.268[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsMapper.xml]'
[2m2025-08-07 08:31:40.284[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsOptionsMapper.xml]'
[2m2025-08-07 08:31:40.300[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamRecordMapper.xml]'
[2m2025-08-07 08:31:40.324[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationInfoMapper.xml]'
[2m2025-08-07 08:31:40.341[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationListInfoMapper.xml]'
[2m2025-08-07 08:31:40.357[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeMapper.xml]'
[2m2025-08-07 08:31:40.374[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeRecordMapper.xml]'
[2m2025-08-07 08:31:40.399[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalResultMapper.xml]'
[2m2025-08-07 08:31:40.414[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormCustomFieldMapper.xml]'
[2m2025-08-07 08:31:40.430[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormGroupMapper.xml]'
[2m2025-08-07 08:31:40.444[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormLimitQuotaMapper.xml]'
[2m2025-08-07 08:31:40.465[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectMapper.xml]'
[2m2025-08-07 08:31:40.480[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectSpecialListMapper.xml]'
[2m2025-08-07 08:31:40.498[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectTemplateFieldMapper.xml]'
[2m2025-08-07 08:31:40.518[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityContentMapper.xml]'
[2m2025-08-07 08:31:40.535[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityFeedbackMapper.xml]'
[2m2025-08-07 08:31:40.554[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityItemMapper.xml]'
[2m2025-08-07 08:31:40.579[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormRestrictMapper.xml]'
[2m2025-08-07 08:31:40.598[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldLinkMapper.xml]'
[2m2025-08-07 08:31:40.618[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldMapper.xml]'
[2m2025-08-07 08:31:40.634[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTypeMapper.xml]'
[2m2025-08-07 08:31:40.651[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoLogMapper.xml]'
[2m2025-08-07 08:31:40.669[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoMapper.xml]'
[2m2025-08-07 08:31:40.684[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkConsultantMapper.xml]'
[2m2025-08-07 08:31:40.702[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselingRecordMapper.xml]'
[2m2025-08-07 08:31:40.722[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselorScheduleMapper.xml]'
[2m2025-08-07 08:31:40.738[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportApprovalNodeMapper.xml]'
[2m2025-08-07 08:31:40.759[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportMapper.xml]'
[2m2025-08-07 08:31:40.774[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportNodeMapper.xml]'
[2m2025-08-07 08:31:40.801[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisRepositoryMapper.xml]'
[2m2025-08-07 08:31:40.821[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkFollowupRecordMapper.xml]'
[2m2025-08-07 08:31:40.837[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkInterpretationMapper.xml]'
[2m2025-08-07 08:31:40.851[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkParamMapper.xml]'
[2m2025-08-07 08:31:40.867[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerInfoMapper.xml]'
[2m2025-08-07 08:31:40.883[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerMapper.xml]'
[2m2025-08-07 08:31:40.899[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyInterpretationMapper.xml]'
[2m2025-08-07 08:31:40.914[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyMapper.xml]'
[2m2025-08-07 08:31:40.930[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyReportMapper.xml]'
[2m2025-08-07 08:31:40.950[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeDataConfigMapper.xml]'
[2m2025-08-07 08:31:40.966[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateMapper.xml]'
[2m2025-08-07 08:31:40.984[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateTypeMapper.xml]'
[2m2025-08-07 08:31:41.000[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/UserResumeMapper.xml]'
[2m2025-08-07 08:31:41.024[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseMarkMapper.xml]'
[2m2025-08-07 08:31:41.037[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CoursePropertiesPercentMapper.xml]'
[2m2025-08-07 08:31:41.056[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreItemMapper.xml]'
[2m2025-08-07 08:31:41.077[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreResultMapper.xml]'
[2m2025-08-07 08:31:41.092[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreLycjMapper.xml]'
[2m2025-08-07 08:31:41.108[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreMycjMapper.xml]'
[2m2025-08-07 08:31:41.126[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTycjMapper.xml]'
[2m2025-08-07 08:31:41.144[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTyhdcyjfMapper.xml]'
[2m2025-08-07 08:31:41.161[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTzjkcsMapper.xml]'
[2m2025-08-07 08:31:41.179[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorAccountMapper.xml]'
[2m2025-08-07 08:31:41.197[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorConfigMapper.xml]'
[2m2025-08-07 08:31:41.213[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorDataMapper.xml]'
[2m2025-08-07 08:31:41.236[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerInfoMapper.xml]'
[2m2025-08-07 08:31:41.252[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerMapper.xml]'
[2m2025-08-07 08:31:41.266[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyClassMapper.xml]'
[2m2025-08-07 08:31:41.284[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyMapper.xml]'
[2m2025-08-07 08:31:41.303[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsMapper.xml]'
[2m2025-08-07 08:31:41.320[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsOptionsMapper.xml]'
[2m2025-08-07 08:31:41.335[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsRuleMapper.xml]'
[2m2025-08-07 08:31:41.350[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQgroupMapper.xml]'
[2m2025-08-07 08:31:41.367[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsClassMapper.xml]'
[2m2025-08-07 08:31:41.388[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsMapper.xml]'
[2m2025-08-07 08:31:41.405[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsOptionsMapper.xml]'
[2m2025-08-07 08:31:41.426[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerInfoMapper.xml]'
[2m2025-08-07 08:31:41.450[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerMapper.xml]'
[2m2025-08-07 08:31:41.466[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpGradeMapper.xml]'
[2m2025-08-07 08:31:41.483[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemMapper.xml]'
[2m2025-08-07 08:31:41.498[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemStaffMapper.xml]'
[2m2025-08-07 08:31:41.516[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpResultMapper.xml]'
[2m2025-08-07 08:31:41.534[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemMapper.xml]'
[2m2025-08-07 08:31:41.549[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemStaffMapper.xml]'
[2m2025-08-07 08:31:41.571[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemFieldMapper.xml]'
[2m2025-08-07 08:31:41.587[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemMapper.xml]'
[2m2025-08-07 08:31:41.609[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemRecordMapper.xml]'
[2m2025-08-07 08:31:41.628[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/QuickSearchMapper.xml]'
[2m2025-08-07 08:31:41.647[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/StudentRecordChangeMapper.xml]'
[2m2025-08-07 08:31:41.662[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserDataScopeMapper.xml]'
[2m2025-08-07 08:31:41.680[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserListInfoMapper.xml]'
[2m2025-08-07 08:31:41.698[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeMapper.xml]'
[2m2025-08-07 08:31:41.720[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeRecordMapper.xml]'
[2m2025-08-07 08:31:41.738[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionDetailMapper.xml]'
[2m2025-08-07 08:31:41.754[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionMapper.xml]'
[2m2025-08-07 08:31:41.776[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowEventMapper.xml]'
[2m2025-08-07 08:31:41.791[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowMapper.xml]'
[2m2025-08-07 08:31:41.808[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeApproverMapper.xml]'
[2m2025-08-07 08:31:41.828[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeFormMapper.xml]'
[2m2025-08-07 08:31:41.846[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeMapper.xml]'
[2m2025-08-07 08:31:41.863[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeStateMapper.xml]'
[2m2025-08-07 08:31:41.880[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxEmployerMapper.xml]'
[2m2025-08-07 08:31:41.899[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxInterviewRecordMapper.xml]'
[2m2025-08-07 08:31:41.913[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxJobTypeMapper.xml]'
[2m2025-08-07 08:31:41.927[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxKsbMapper.xml]'
[2m2025-08-07 08:31:41.941[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxStudentsClassTimeMapper.xml]'
[2m2025-08-07 08:31:41.952[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:22
[2m2025-08-07 08:31:42.287[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-07 08:31:42.369[0;39m [31mERROR[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-07 08:31:42.719[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-07 08:31:45.502[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-07 08:31:45.699[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.c.s.JwtAuthenticationFilter       [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-07 08:31:45.929[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-07 08:31:45.979[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-07 08:31:46.033[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-07 08:31:46.297[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-07 08:31:46.400[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-07 08:31:47.668[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-07 08:31:47.871[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-07 08:31:38",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1250416506, ConnectTime:"2025-08-07 08:31:47", UseCount:1, LastActiveTime:"2025-08-07 08:31:47"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-07 08:31:50.660[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-07 08:31:50.669[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-07 08:31:51.112[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m JSqlParser is in classpath; If applicable, JSqlParser will be used.
[2m2025-08-07 08:31:51.112[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
[2m2025-08-07 08:32:00.828[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 4785323f-6973-4742-ae2d-fe5f30b1a3c6

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-07 08:32:00.842[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-07 08:32:01.397[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 4785323f-6973-4742-ae2d-fe5f30b1a3c6

[2m2025-08-07 08:32:01.689[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-8082"]
[2m2025-08-07 08:32:01.728[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8082 (http) with context path '/'
[2m2025-08-07 08:32:01.744[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Started AdminApplication in 27.896 seconds (process running for 29.661)
[2m2025-08-07 08:32:01.747[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-07 08:32:01.748[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-07 08:32:11.358[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-07 08:32:11.406[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-07 08:32:11.412[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-07 08:32:11.427[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-07 08:32:11.464[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-07 08:32:11.607[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 08:34:41.201[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 08:34:41.215[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
[2m2025-08-07 08:34:41.216[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 08:34:41.223[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 08:34:41.303[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 08:34:41.316[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 10 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-07 08:34:41.317[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.sanythadmin.**.mapper]' package. Please check your configuration.
