[2m2025-08-07 08:31:34.442[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-07 08:31:34.688[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Starting AdminApplication using Java 17.0.15 with PID 3856 (/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-xuegong-5)
[2m2025-08-07 08:31:34.689[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-07 08:31:34.689[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-07 08:31:36.257[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-07 08:31:36.259[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 08:31:36.578[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: jakarta.persistence.Entity, jakarta.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository
[2m2025-08-07 08:31:36.728[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 449 ms. Found 27 JPA repository interfaces.
[2m2025-08-07 08:31:36.771[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-07 08:31:36.771[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 08:31:36.808[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.808[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitEventTypeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.809[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.811[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.811[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.811[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.812[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.813[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.813[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.813[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 08:31:36.841[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 68 ms. Found 1 MongoDB repository interface.
[2m2025-08-07 08:31:36.856[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-07 08:31:36.857[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.900[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitEventTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.901[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 08:31:36.902[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 08:31:37.352[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
[2m2025-08-07 08:31:37.905[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8082 (http)
[2m2025-08-07 08:31:37.925[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-8082"]
[2m2025-08-07 08:31:37.926[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-07 08:31:37.926[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-07 08:31:37.987[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-07 08:31:37.987[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3239 ms
[2m2025-08-07 08:31:38.139[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.6"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@6587c92f, com.mongodb.Jep395RecordCodecProvider@537979aa, com.mongodb.KotlinCodecProvider@5ac25491]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-07 08:31:38.258[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=66108208, minRoundTripTimeNanos=0}
[2m2025-08-07 08:31:38.496[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-07 08:31:38.715[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-07 08:31:38.720[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanythadmin.common.core.mybatisplus.interceptor.DecryptInterceptor@3e13a74'
[2m2025-08-07 08:31:38.720[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@71e064b2'
[2m2025-08-07 08:31:38.720[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@20bd4fd2'
[2m2025-08-07 08:31:38.918[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/EmailRecordMapper.xml]'
[2m2025-08-07 08:31:38.973[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-07 08:31:39.004[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-07 08:31:39.039[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-07 08:31:39.061[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-07 08:31:39.080[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysJwtMapper.xml]'
[2m2025-08-07 08:31:39.127[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-07 08:31:39.155[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysModuleSetupMapper.xml]'
[2m2025-08-07 08:31:39.183[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-07 08:31:39.203[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-07 08:31:39.225[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-07 08:31:39.281[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-07 08:31:39.299[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserOrgMapMapper.xml]'
[2m2025-08-07 08:31:39.318[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinAddressMapper.xml]'
[2m2025-08-07 08:31:39.342[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemAddressMapper.xml]'
[2m2025-08-07 08:31:39.365[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemMapper.xml]'
[2m2025-08-07 08:31:39.391[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinRecordMapper.xml]'
[2m2025-08-07 08:31:39.414[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-07 08:31:39.437[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-07 08:31:39.457[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-07 08:31:39.473[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeMzbMapper.xml]'
[2m2025-08-07 08:31:39.489[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeNjbMapper.xml]'
[2m2025-08-07 08:31:39.505[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodePyccbMapper.xml]'
[2m2025-08-07 08:31:39.526[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-07 08:31:39.548[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXqbMapper.xml]'
[2m2025-08-07 08:31:39.564[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXsztMapper.xml]'
[2m2025-08-07 08:31:39.579[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-07 08:31:39.593[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZzmmbMapper.xml]'
[2m2025-08-07 08:31:39.610[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/ScoreSourceConfigMapper.xml]'
[2m2025-08-07 08:31:39.630[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionFieldMapper.xml]'
[2m2025-08-07 08:31:39.646[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionMapper.xml]'
[2m2025-08-07 08:31:39.665[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldEduLevelMapper.xml]'
[2m2025-08-07 08:31:39.684[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldHideRoleMapper.xml]'
[2m2025-08-07 08:31:39.704[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldLinkMapper.xml]'
[2m2025-08-07 08:31:39.725[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldMapper.xml]'
[2m2025-08-07 08:31:39.739[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupEduLevelMapper.xml]'
[2m2025-08-07 08:31:39.753[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupHideRoleMapper.xml]'
[2m2025-08-07 08:31:39.770[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupMapper.xml]'
[2m2025-08-07 08:31:39.784[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/ListGroupConfigMapper.xml]'
[2m2025-08-07 08:31:39.798[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/SelectControlApiMapper.xml]'
[2m2025-08-07 08:31:39.832[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationInfoMapper.xml]'
[2m2025-08-07 08:31:39.854[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationReferenceInfoMapper.xml]'
[2m2025-08-07 08:31:39.879[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeMapper.xml]'
[2m2025-08-07 08:31:39.900[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeRecordMapper.xml]'
[2m2025-08-07 08:31:39.917[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigMapper.xml]'
[2m2025-08-07 08:31:39.932[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigScopeMapper.xml]'
[2m2025-08-07 08:31:39.949[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailMapper.xml]'
[2m2025-08-07 08:31:39.963[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailScopeMapper.xml]'
[2m2025-08-07 08:31:39.980[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemMapper.xml]'
[2m2025-08-07 08:31:39.996[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluatePeerReviewRecordMapper.xml]'
[2m2025-08-07 08:31:40.011[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateReviewScoreMapper.xml]'
[2m2025-08-07 08:31:40.032[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreConfirmRecordMapper.xml]'
[2m2025-08-07 08:31:40.056[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreMapper.xml]'
[2m2025-08-07 08:31:40.084[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerInfoMapper.xml]'
[2m2025-08-07 08:31:40.102[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerMapper.xml]'
[2m2025-08-07 08:31:40.118[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamClassMapper.xml]'
[2m2025-08-07 08:31:40.137[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamMapper.xml]'
[2m2025-08-07 08:31:40.152[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperClassMapper.xml]'
[2m2025-08-07 08:31:40.155[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperContentMapper.xml]'
[2m2025-08-07 08:31:40.173[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperMapper.xml]'
[2m2025-08-07 08:31:40.187[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQgroupMapper.xml]'
[2m2025-08-07 08:31:40.203[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsMapper.xml]'
[2m2025-08-07 08:31:40.224[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsOptionsMapper.xml]'
[2m2025-08-07 08:31:40.238[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsRuleMapper.xml]'
[2m2025-08-07 08:31:40.252[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsClassMapper.xml]'
[2m2025-08-07 08:31:40.268[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsMapper.xml]'
[2m2025-08-07 08:31:40.284[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsOptionsMapper.xml]'
[2m2025-08-07 08:31:40.300[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamRecordMapper.xml]'
[2m2025-08-07 08:31:40.324[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationInfoMapper.xml]'
[2m2025-08-07 08:31:40.341[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationListInfoMapper.xml]'
[2m2025-08-07 08:31:40.357[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeMapper.xml]'
[2m2025-08-07 08:31:40.374[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeRecordMapper.xml]'
[2m2025-08-07 08:31:40.399[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalResultMapper.xml]'
[2m2025-08-07 08:31:40.414[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormCustomFieldMapper.xml]'
[2m2025-08-07 08:31:40.430[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormGroupMapper.xml]'
[2m2025-08-07 08:31:40.444[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormLimitQuotaMapper.xml]'
[2m2025-08-07 08:31:40.465[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectMapper.xml]'
[2m2025-08-07 08:31:40.480[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectSpecialListMapper.xml]'
[2m2025-08-07 08:31:40.498[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectTemplateFieldMapper.xml]'
[2m2025-08-07 08:31:40.518[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityContentMapper.xml]'
[2m2025-08-07 08:31:40.535[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityFeedbackMapper.xml]'
[2m2025-08-07 08:31:40.554[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityItemMapper.xml]'
[2m2025-08-07 08:31:40.579[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormRestrictMapper.xml]'
[2m2025-08-07 08:31:40.598[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldLinkMapper.xml]'
[2m2025-08-07 08:31:40.618[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldMapper.xml]'
[2m2025-08-07 08:31:40.634[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTypeMapper.xml]'
[2m2025-08-07 08:31:40.651[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoLogMapper.xml]'
[2m2025-08-07 08:31:40.669[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoMapper.xml]'
[2m2025-08-07 08:31:40.684[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkConsultantMapper.xml]'
[2m2025-08-07 08:31:40.702[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselingRecordMapper.xml]'
[2m2025-08-07 08:31:40.722[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselorScheduleMapper.xml]'
[2m2025-08-07 08:31:40.738[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportApprovalNodeMapper.xml]'
[2m2025-08-07 08:31:40.759[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportMapper.xml]'
[2m2025-08-07 08:31:40.774[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportNodeMapper.xml]'
[2m2025-08-07 08:31:40.801[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisRepositoryMapper.xml]'
[2m2025-08-07 08:31:40.821[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkFollowupRecordMapper.xml]'
[2m2025-08-07 08:31:40.837[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkInterpretationMapper.xml]'
[2m2025-08-07 08:31:40.851[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkParamMapper.xml]'
[2m2025-08-07 08:31:40.867[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerInfoMapper.xml]'
[2m2025-08-07 08:31:40.883[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerMapper.xml]'
[2m2025-08-07 08:31:40.899[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyInterpretationMapper.xml]'
[2m2025-08-07 08:31:40.914[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyMapper.xml]'
[2m2025-08-07 08:31:40.930[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyReportMapper.xml]'
[2m2025-08-07 08:31:40.950[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeDataConfigMapper.xml]'
[2m2025-08-07 08:31:40.966[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateMapper.xml]'
[2m2025-08-07 08:31:40.984[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateTypeMapper.xml]'
[2m2025-08-07 08:31:41.000[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/UserResumeMapper.xml]'
[2m2025-08-07 08:31:41.024[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseMarkMapper.xml]'
[2m2025-08-07 08:31:41.037[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CoursePropertiesPercentMapper.xml]'
[2m2025-08-07 08:31:41.056[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreItemMapper.xml]'
[2m2025-08-07 08:31:41.077[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreResultMapper.xml]'
[2m2025-08-07 08:31:41.092[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreLycjMapper.xml]'
[2m2025-08-07 08:31:41.108[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreMycjMapper.xml]'
[2m2025-08-07 08:31:41.126[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTycjMapper.xml]'
[2m2025-08-07 08:31:41.144[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTyhdcyjfMapper.xml]'
[2m2025-08-07 08:31:41.161[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTzjkcsMapper.xml]'
[2m2025-08-07 08:31:41.179[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorAccountMapper.xml]'
[2m2025-08-07 08:31:41.197[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorConfigMapper.xml]'
[2m2025-08-07 08:31:41.213[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorDataMapper.xml]'
[2m2025-08-07 08:31:41.236[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerInfoMapper.xml]'
[2m2025-08-07 08:31:41.252[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerMapper.xml]'
[2m2025-08-07 08:31:41.266[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyClassMapper.xml]'
[2m2025-08-07 08:31:41.284[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyMapper.xml]'
[2m2025-08-07 08:31:41.303[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsMapper.xml]'
[2m2025-08-07 08:31:41.320[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsOptionsMapper.xml]'
[2m2025-08-07 08:31:41.335[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsRuleMapper.xml]'
[2m2025-08-07 08:31:41.350[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQgroupMapper.xml]'
[2m2025-08-07 08:31:41.367[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsClassMapper.xml]'
[2m2025-08-07 08:31:41.388[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsMapper.xml]'
[2m2025-08-07 08:31:41.405[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsOptionsMapper.xml]'
[2m2025-08-07 08:31:41.426[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerInfoMapper.xml]'
[2m2025-08-07 08:31:41.450[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerMapper.xml]'
[2m2025-08-07 08:31:41.466[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpGradeMapper.xml]'
[2m2025-08-07 08:31:41.483[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemMapper.xml]'
[2m2025-08-07 08:31:41.498[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemStaffMapper.xml]'
[2m2025-08-07 08:31:41.516[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpResultMapper.xml]'
[2m2025-08-07 08:31:41.534[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemMapper.xml]'
[2m2025-08-07 08:31:41.549[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemStaffMapper.xml]'
[2m2025-08-07 08:31:41.571[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemFieldMapper.xml]'
[2m2025-08-07 08:31:41.587[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemMapper.xml]'
[2m2025-08-07 08:31:41.609[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemRecordMapper.xml]'
[2m2025-08-07 08:31:41.628[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/QuickSearchMapper.xml]'
[2m2025-08-07 08:31:41.647[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/StudentRecordChangeMapper.xml]'
[2m2025-08-07 08:31:41.662[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserDataScopeMapper.xml]'
[2m2025-08-07 08:31:41.680[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserListInfoMapper.xml]'
[2m2025-08-07 08:31:41.698[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeMapper.xml]'
[2m2025-08-07 08:31:41.720[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeRecordMapper.xml]'
[2m2025-08-07 08:31:41.738[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionDetailMapper.xml]'
[2m2025-08-07 08:31:41.754[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionMapper.xml]'
[2m2025-08-07 08:31:41.776[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowEventMapper.xml]'
[2m2025-08-07 08:31:41.791[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowMapper.xml]'
[2m2025-08-07 08:31:41.808[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeApproverMapper.xml]'
[2m2025-08-07 08:31:41.828[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeFormMapper.xml]'
[2m2025-08-07 08:31:41.846[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeMapper.xml]'
[2m2025-08-07 08:31:41.863[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeStateMapper.xml]'
[2m2025-08-07 08:31:41.880[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxEmployerMapper.xml]'
[2m2025-08-07 08:31:41.899[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxInterviewRecordMapper.xml]'
[2m2025-08-07 08:31:41.913[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxJobTypeMapper.xml]'
[2m2025-08-07 08:31:41.927[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxKsbMapper.xml]'
[2m2025-08-07 08:31:41.941[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxStudentsClassTimeMapper.xml]'
[2m2025-08-07 08:31:41.952[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:22
[2m2025-08-07 08:31:42.287[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-07 08:31:42.369[0;39m [31mERROR[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-07 08:31:42.719[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-07 08:31:45.502[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-07 08:31:45.699[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.c.s.JwtAuthenticationFilter       [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-07 08:31:45.929[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-07 08:31:45.979[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-07 08:31:46.033[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-07 08:31:46.297[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-07 08:31:46.400[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-07 08:31:47.668[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-07 08:31:47.871[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-07 08:31:38",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1250416506, ConnectTime:"2025-08-07 08:31:47", UseCount:1, LastActiveTime:"2025-08-07 08:31:47"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-07 08:31:50.660[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-07 08:31:50.669[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-07 08:31:51.112[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m JSqlParser is in classpath; If applicable, JSqlParser will be used.
[2m2025-08-07 08:31:51.112[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
[2m2025-08-07 08:32:00.828[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 4785323f-6973-4742-ae2d-fe5f30b1a3c6

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-07 08:32:00.842[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-07 08:32:01.397[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 4785323f-6973-4742-ae2d-fe5f30b1a3c6

[2m2025-08-07 08:32:01.689[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-8082"]
[2m2025-08-07 08:32:01.728[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8082 (http) with context path '/'
[2m2025-08-07 08:32:01.744[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Started AdminApplication in 27.896 seconds (process running for 29.661)
[2m2025-08-07 08:32:01.747[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-07 08:32:01.748[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-07 08:32:11.358[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-07 08:32:11.406[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-07 08:32:11.412[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-07 08:32:11.427[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-07 08:32:11.464[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-07 08:32:11.607[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 08:34:41.201[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 08:34:41.215[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
[2m2025-08-07 08:34:41.216[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 08:34:41.223[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 08:34:41.303[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 08:34:41.316[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 10 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-07 08:34:41.317[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.sanythadmin.**.mapper]' package. Please check your configuration.
[2m2025-08-07 09:31:12.053[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 09:31:12.071[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
[2m2025-08-07 09:31:12.072[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 09:31:12.078[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 09:31:12.097[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 09:31:12.103[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 3 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-07 09:31:12.105[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.sanythadmin.**.mapper]' package. Please check your configuration.
[2m2025-08-07 09:36:10.454[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 09:36:11.155[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 696 ms. Found 19 JPA repository interfaces.
[2m2025-08-07 09:36:11.156[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 09:36:11.292[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.292[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.293[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.293[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.293[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.293[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.293[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.293[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.293[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.293[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:36:11.294[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 137 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 09:36:11.725[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 09:36:11.874[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.874[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.875[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.875[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.875[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.875[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.875[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.875[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.875[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.875[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.875[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.876[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.876[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.876[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.876[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.876[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.876[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.876[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.876[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:36:11.877[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 150 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-07 09:36:11.886[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.sanythadmin.**.mapper]' package. Please check your configuration.
[2m2025-08-07 09:36:19.784[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-07 09:36:19.814[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-07 09:36:20.733[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-07 09:36:20.926[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-07 08:31:38",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:2,
	DestroyCount:1,
	CloseCount:3,
	ConnectCount:3,
	Connections:[
		{ID:638652554, ConnectTime:"2025-08-07 09:36:20", UseCount:1, LastActiveTime:"2025-08-07 09:36:20"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-07 09:36:22.250[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-07 09:36:58.002[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 09:36:58.008[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 JPA repository interfaces.
[2m2025-08-07 09:36:58.009[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 09:36:58.012[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 09:36:58.151[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 09:36:58.156[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 3 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-07 09:36:58.156[0;39m [33m WARN[0;39m [35m3856[0;39m [2m---[0;39m [2m[detector-thread][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.sanythadmin.**.mapper]' package. Please check your configuration.
[2m2025-08-07 09:38:47.012[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-07 09:38:47.012[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-07 09:38:47.044[0;39m [32m INFO[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 31 ms
[2m2025-08-07 09:38:47.746[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 09:38:47.760[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 09:38:47.763[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 09:38:47.846[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 09:38:47.851[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 用人单位(String)
[2m2025-08-07 09:38:47.933[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:38:48.046[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,TOKEN_EXPIRE_TIME,TOKEN_REFRESH_TIME,TOKEN_KEY,CREATE_TIME    FROM  SYT_SYS_JWT
[2m2025-08-07 09:38:48.053[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TOKEN_EXPIRE_TIME,TOKEN_REFRESH_TIME,TOKEN_KEY,CREATE_TIME    FROM  SYT_SYS_JWT
[2m2025-08-07 09:38:48.055[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TOKEN_EXPIRE_TIME, TOKEN_REFRESH_TIME, TOKEN_KEY, CREATE_TIME FROM SYT_SYS_JWT
[2m2025-08-07 09:38:48.056[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TOKEN_EXPIRE_TIME, TOKEN_REFRESH_TIME, TOKEN_KEY, CREATE_TIME FROM SYT_SYS_JWT
[2m2025-08-07 09:38:48.056[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-07 09:38:48.150[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:38:55.412[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 09:38:55.426[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 09:38:55.428[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 09:38:55.429[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 09:38:55.429[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 用人单位(String)
[2m2025-08-07 09:38:55.501[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:38:55.920[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:38:55.933[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:38:55.936[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:38:55.938[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:38:55.939[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xy001(String), xy001(String)
[2m2025-08-07 09:38:56.012[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:38:56.015[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 09:38:56.062[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 09:38:56.065[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 09:38:56.066[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 09:38:56.066[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 760760c056ae97081e9a7456a7ef19ba(String)
[2m2025-08-07 09:38:56.132[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-07 09:38:57.377[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 09:38:57.391[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 09:38:57.393[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 09:38:57.394[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 09:38:57.395[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-08-07 09:38:57.484[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:38:57.492[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,YGKSRQ,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS    FROM  SYT_QGZX_STUDENT_APPLY         WHERE  (JOB_ID = ? AND YGZT = ?) ORDER BY SQSJ DESC
[2m2025-08-07 09:38:57.502[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,XGH,JOB_ID,XNXQ,SQSJ,sqly,TCYS,SFFCAP,SPZT,YGZT,YGKSRQ,WORKFLOW_ID,TJZT,ORIGINAL_JOB_ID,TJSQSJ,TJQRSJ,XSQRSJ,ROLE_ID,XXMC,SFTS    FROM  SYT_QGZX_STUDENT_APPLY         WHERE  (JOB_ID = ? AND YGZT = ?) ORDER BY SQSJ DESC
[2m2025-08-07 09:38:57.504[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, YGKSRQ, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE (JOB_ID = ? AND YGZT = ?) ORDER BY SQSJ DESC
[2m2025-08-07 09:38:57.505[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, XGH, JOB_ID, XNXQ, SQSJ, sqly, TCYS, SFFCAP, SPZT, YGZT, YGKSRQ, WORKFLOW_ID, TJZT, ORIGINAL_JOB_ID, TJSQSJ, TJQRSJ, XSQRSJ, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_STUDENT_APPLY WHERE (JOB_ID = ? AND YGZT = ?) ORDER BY SQSJ DESC
[2m2025-08-07 09:38:57.506[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String), YGJS(String)
[2m2025-08-07 09:38:57.596[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:38:57.869[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-07 09:38:57.899[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-07 09:38:57.904[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-07 09:38:57.913[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==>  Preparing: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-07 09:38:57.914[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==> Parameters: 2023000914(String), 2023000914(String)
[2m2025-08-07 09:38:58.012[0;39m [32mDEBUG[0;39m [35m3856[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:47:24.390[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-07 09:47:24.616[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Starting AdminApplication using Java 17.0.15 with PID 25584 (/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-xuegong-5)
[2m2025-08-07 09:47:24.617[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-07 09:47:24.618[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-07 09:47:26.092[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-07 09:47:26.095[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 09:47:26.394[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: jakarta.persistence.Entity, jakarta.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository
[2m2025-08-07 09:47:26.537[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 429 ms. Found 27 JPA repository interfaces.
[2m2025-08-07 09:47:26.580[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-07 09:47:26.580[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 09:47:26.615[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.616[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.616[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.616[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.616[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.616[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.617[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitEventTypeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.617[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.617[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.617[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.618[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.619[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.620[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.620[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.620[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.620[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.620[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.620[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-07 09:47:26.647[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 65 ms. Found 1 MongoDB repository interface.
[2m2025-08-07 09:47:26.661[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-07 09:47:26.662[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 09:47:26.703[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.703[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.703[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.703[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.703[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitEventTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.704[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.705[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.705[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.705[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.705[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.705[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.705[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.705[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.705[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.705[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.706[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.706[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-07 09:47:26.706[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 09:47:27.095[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
[2m2025-08-07 09:47:27.625[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8082 (http)
[2m2025-08-07 09:47:27.645[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-8082"]
[2m2025-08-07 09:47:27.646[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-07 09:47:27.646[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-07 09:47:27.694[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-07 09:47:27.694[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3022 ms
[2m2025-08-07 09:47:27.850[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.6"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@770635f8, com.mongodb.Jep395RecordCodecProvider@7887e1f3, com.mongodb.KotlinCodecProvider@199d1fb7]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-07 09:47:27.988[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=72680000, minRoundTripTimeNanos=0}
[2m2025-08-07 09:47:28.205[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-07 09:47:28.409[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-07 09:47:28.415[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanythadmin.common.core.mybatisplus.interceptor.DecryptInterceptor@2174fda1'
[2m2025-08-07 09:47:28.415[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@6a636c62'
[2m2025-08-07 09:47:28.415[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@7aadb5e8'
[2m2025-08-07 09:47:28.622[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/EmailRecordMapper.xml]'
[2m2025-08-07 09:47:28.680[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-07 09:47:28.714[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-07 09:47:28.751[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-07 09:47:28.776[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-07 09:47:28.799[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysJwtMapper.xml]'
[2m2025-08-07 09:47:28.851[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-07 09:47:28.882[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysModuleSetupMapper.xml]'
[2m2025-08-07 09:47:28.905[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-07 09:47:28.922[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-07 09:47:28.941[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-07 09:47:28.993[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-07 09:47:29.011[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserOrgMapMapper.xml]'
[2m2025-08-07 09:47:29.032[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinAddressMapper.xml]'
[2m2025-08-07 09:47:29.056[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemAddressMapper.xml]'
[2m2025-08-07 09:47:29.079[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemMapper.xml]'
[2m2025-08-07 09:47:29.108[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinRecordMapper.xml]'
[2m2025-08-07 09:47:29.130[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-07 09:47:29.151[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-07 09:47:29.168[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-07 09:47:29.185[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeMzbMapper.xml]'
[2m2025-08-07 09:47:29.210[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeNjbMapper.xml]'
[2m2025-08-07 09:47:29.227[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodePyccbMapper.xml]'
[2m2025-08-07 09:47:29.243[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-07 09:47:29.259[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXqbMapper.xml]'
[2m2025-08-07 09:47:29.277[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXsztMapper.xml]'
[2m2025-08-07 09:47:29.293[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-07 09:47:29.309[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZzmmbMapper.xml]'
[2m2025-08-07 09:47:29.326[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/ScoreSourceConfigMapper.xml]'
[2m2025-08-07 09:47:29.354[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionFieldMapper.xml]'
[2m2025-08-07 09:47:29.375[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionMapper.xml]'
[2m2025-08-07 09:47:29.395[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldEduLevelMapper.xml]'
[2m2025-08-07 09:47:29.412[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldHideRoleMapper.xml]'
[2m2025-08-07 09:47:29.431[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldLinkMapper.xml]'
[2m2025-08-07 09:47:29.452[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldMapper.xml]'
[2m2025-08-07 09:47:29.466[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupEduLevelMapper.xml]'
[2m2025-08-07 09:47:29.480[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupHideRoleMapper.xml]'
[2m2025-08-07 09:47:29.499[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupMapper.xml]'
[2m2025-08-07 09:47:29.516[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/ListGroupConfigMapper.xml]'
[2m2025-08-07 09:47:29.537[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/SelectControlApiMapper.xml]'
[2m2025-08-07 09:47:29.567[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationInfoMapper.xml]'
[2m2025-08-07 09:47:29.583[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationReferenceInfoMapper.xml]'
[2m2025-08-07 09:47:29.603[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeMapper.xml]'
[2m2025-08-07 09:47:29.620[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeRecordMapper.xml]'
[2m2025-08-07 09:47:29.635[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigMapper.xml]'
[2m2025-08-07 09:47:29.650[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigScopeMapper.xml]'
[2m2025-08-07 09:47:29.665[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailMapper.xml]'
[2m2025-08-07 09:47:29.679[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailScopeMapper.xml]'
[2m2025-08-07 09:47:29.695[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemMapper.xml]'
[2m2025-08-07 09:47:29.717[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluatePeerReviewRecordMapper.xml]'
[2m2025-08-07 09:47:29.735[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateReviewScoreMapper.xml]'
[2m2025-08-07 09:47:29.751[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreConfirmRecordMapper.xml]'
[2m2025-08-07 09:47:29.775[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreMapper.xml]'
[2m2025-08-07 09:47:29.800[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerInfoMapper.xml]'
[2m2025-08-07 09:47:29.819[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerMapper.xml]'
[2m2025-08-07 09:47:29.834[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamClassMapper.xml]'
[2m2025-08-07 09:47:29.853[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamMapper.xml]'
[2m2025-08-07 09:47:29.868[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperClassMapper.xml]'
[2m2025-08-07 09:47:29.871[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperContentMapper.xml]'
[2m2025-08-07 09:47:29.896[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperMapper.xml]'
[2m2025-08-07 09:47:29.913[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQgroupMapper.xml]'
[2m2025-08-07 09:47:29.931[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsMapper.xml]'
[2m2025-08-07 09:47:29.948[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsOptionsMapper.xml]'
[2m2025-08-07 09:47:29.964[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsRuleMapper.xml]'
[2m2025-08-07 09:47:29.980[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsClassMapper.xml]'
[2m2025-08-07 09:47:29.997[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsMapper.xml]'
[2m2025-08-07 09:47:30.014[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsOptionsMapper.xml]'
[2m2025-08-07 09:47:30.030[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamRecordMapper.xml]'
[2m2025-08-07 09:47:30.054[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationInfoMapper.xml]'
[2m2025-08-07 09:47:30.079[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationListInfoMapper.xml]'
[2m2025-08-07 09:47:30.099[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeMapper.xml]'
[2m2025-08-07 09:47:30.118[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeRecordMapper.xml]'
[2m2025-08-07 09:47:30.139[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalResultMapper.xml]'
[2m2025-08-07 09:47:30.156[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormCustomFieldMapper.xml]'
[2m2025-08-07 09:47:30.174[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormGroupMapper.xml]'
[2m2025-08-07 09:47:30.191[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormLimitQuotaMapper.xml]'
[2m2025-08-07 09:47:30.213[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectMapper.xml]'
[2m2025-08-07 09:47:30.229[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectSpecialListMapper.xml]'
[2m2025-08-07 09:47:30.246[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectTemplateFieldMapper.xml]'
[2m2025-08-07 09:47:30.263[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityContentMapper.xml]'
[2m2025-08-07 09:47:30.284[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityFeedbackMapper.xml]'
[2m2025-08-07 09:47:30.301[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityItemMapper.xml]'
[2m2025-08-07 09:47:30.317[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormRestrictMapper.xml]'
[2m2025-08-07 09:47:30.334[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldLinkMapper.xml]'
[2m2025-08-07 09:47:30.351[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldMapper.xml]'
[2m2025-08-07 09:47:30.367[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTypeMapper.xml]'
[2m2025-08-07 09:47:30.386[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoLogMapper.xml]'
[2m2025-08-07 09:47:30.405[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoMapper.xml]'
[2m2025-08-07 09:47:30.422[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkConsultantMapper.xml]'
[2m2025-08-07 09:47:30.440[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselingRecordMapper.xml]'
[2m2025-08-07 09:47:30.459[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselorScheduleMapper.xml]'
[2m2025-08-07 09:47:30.476[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportApprovalNodeMapper.xml]'
[2m2025-08-07 09:47:30.502[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportMapper.xml]'
[2m2025-08-07 09:47:30.518[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportNodeMapper.xml]'
[2m2025-08-07 09:47:30.538[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisRepositoryMapper.xml]'
[2m2025-08-07 09:47:30.556[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkFollowupRecordMapper.xml]'
[2m2025-08-07 09:47:30.572[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkInterpretationMapper.xml]'
[2m2025-08-07 09:47:30.587[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkParamMapper.xml]'
[2m2025-08-07 09:47:30.607[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerInfoMapper.xml]'
[2m2025-08-07 09:47:30.627[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerMapper.xml]'
[2m2025-08-07 09:47:30.644[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyInterpretationMapper.xml]'
[2m2025-08-07 09:47:30.661[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyMapper.xml]'
[2m2025-08-07 09:47:30.676[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyReportMapper.xml]'
[2m2025-08-07 09:47:30.695[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeDataConfigMapper.xml]'
[2m2025-08-07 09:47:30.716[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateMapper.xml]'
[2m2025-08-07 09:47:30.735[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateTypeMapper.xml]'
[2m2025-08-07 09:47:30.752[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/UserResumeMapper.xml]'
[2m2025-08-07 09:47:30.773[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseMarkMapper.xml]'
[2m2025-08-07 09:47:30.787[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CoursePropertiesPercentMapper.xml]'
[2m2025-08-07 09:47:30.808[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreItemMapper.xml]'
[2m2025-08-07 09:47:30.832[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreResultMapper.xml]'
[2m2025-08-07 09:47:30.850[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreLycjMapper.xml]'
[2m2025-08-07 09:47:30.867[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreMycjMapper.xml]'
[2m2025-08-07 09:47:30.884[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTycjMapper.xml]'
[2m2025-08-07 09:47:30.900[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTyhdcyjfMapper.xml]'
[2m2025-08-07 09:47:30.925[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTzjkcsMapper.xml]'
[2m2025-08-07 09:47:30.945[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorAccountMapper.xml]'
[2m2025-08-07 09:47:30.964[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorConfigMapper.xml]'
[2m2025-08-07 09:47:30.982[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorDataMapper.xml]'
[2m2025-08-07 09:47:31.004[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerInfoMapper.xml]'
[2m2025-08-07 09:47:31.020[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerMapper.xml]'
[2m2025-08-07 09:47:31.035[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyClassMapper.xml]'
[2m2025-08-07 09:47:31.056[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyMapper.xml]'
[2m2025-08-07 09:47:31.075[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsMapper.xml]'
[2m2025-08-07 09:47:31.092[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsOptionsMapper.xml]'
[2m2025-08-07 09:47:31.107[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsRuleMapper.xml]'
[2m2025-08-07 09:47:31.121[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQgroupMapper.xml]'
[2m2025-08-07 09:47:31.136[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsClassMapper.xml]'
[2m2025-08-07 09:47:31.153[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsMapper.xml]'
[2m2025-08-07 09:47:31.167[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsOptionsMapper.xml]'
[2m2025-08-07 09:47:31.195[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerInfoMapper.xml]'
[2m2025-08-07 09:47:31.219[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerMapper.xml]'
[2m2025-08-07 09:47:31.235[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpGradeMapper.xml]'
[2m2025-08-07 09:47:31.252[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemMapper.xml]'
[2m2025-08-07 09:47:31.268[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemStaffMapper.xml]'
[2m2025-08-07 09:47:31.283[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpResultMapper.xml]'
[2m2025-08-07 09:47:31.300[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemMapper.xml]'
[2m2025-08-07 09:47:31.314[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemStaffMapper.xml]'
[2m2025-08-07 09:47:31.335[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemFieldMapper.xml]'
[2m2025-08-07 09:47:31.350[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemMapper.xml]'
[2m2025-08-07 09:47:31.371[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemRecordMapper.xml]'
[2m2025-08-07 09:47:31.388[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/QuickSearchMapper.xml]'
[2m2025-08-07 09:47:31.407[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/StudentRecordChangeMapper.xml]'
[2m2025-08-07 09:47:31.423[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserDataScopeMapper.xml]'
[2m2025-08-07 09:47:31.450[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserListInfoMapper.xml]'
[2m2025-08-07 09:47:31.470[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeMapper.xml]'
[2m2025-08-07 09:47:31.487[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeRecordMapper.xml]'
[2m2025-08-07 09:47:31.504[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionDetailMapper.xml]'
[2m2025-08-07 09:47:31.519[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionMapper.xml]'
[2m2025-08-07 09:47:31.537[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowEventMapper.xml]'
[2m2025-08-07 09:47:31.551[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowMapper.xml]'
[2m2025-08-07 09:47:31.566[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeApproverMapper.xml]'
[2m2025-08-07 09:47:31.582[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeFormMapper.xml]'
[2m2025-08-07 09:47:31.598[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeMapper.xml]'
[2m2025-08-07 09:47:31.615[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeStateMapper.xml]'
[2m2025-08-07 09:47:31.634[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxEmployerMapper.xml]'
[2m2025-08-07 09:47:31.656[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxInterviewRecordMapper.xml]'
[2m2025-08-07 09:47:31.680[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxJobTypeMapper.xml]'
[2m2025-08-07 09:47:31.698[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxKsbMapper.xml]'
[2m2025-08-07 09:47:31.714[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxStudentsClassTimeMapper.xml]'
[2m2025-08-07 09:47:31.725[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:14 workerId:11
[2m2025-08-07 09:47:32.055[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-07 09:47:32.143[0;39m [31mERROR[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-07 09:47:32.514[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-07 09:47:35.881[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-07 09:47:36.141[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.c.s.JwtAuthenticationFilter       [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-07 09:47:36.383[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-07 09:47:36.467[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-07 09:47:36.526[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-07 09:47:36.793[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-07 09:47:36.896[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-07 09:47:38.281[0;39m [33m WARN[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-07 09:47:38.523[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-07 09:47:28",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:2111213983, ConnectTime:"2025-08-07 09:47:37", UseCount:1, LastActiveTime:"2025-08-07 09:47:38"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-07 09:47:41.896[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-07 09:47:41.907[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-07 09:47:42.345[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m JSqlParser is in classpath; If applicable, JSqlParser will be used.
[2m2025-08-07 09:47:42.345[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
[2m2025-08-07 09:47:52.096[0;39m [33m WARN[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 0fbe3205-e95d-4209-9711-9c7b5a4e765a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-07 09:47:52.109[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-07 09:47:52.644[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 0fbe3205-e95d-4209-9711-9c7b5a4e765a

[2m2025-08-07 09:47:52.919[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-8082"]
[2m2025-08-07 09:47:52.956[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8082 (http) with context path '/'
[2m2025-08-07 09:47:52.972[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Started AdminApplication in 29.366 seconds (process running for 31.979)
[2m2025-08-07 09:47:52.975[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-07 09:47:52.976[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-07 09:47:55.813[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-07 09:47:55.859[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-07 09:47:55.864[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-07 09:47:55.883[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-07 09:47:55.929[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-07 09:47:56.111[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:47:57.222[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-07 09:47:57.222[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-07 09:47:57.228[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 6 ms
[2m2025-08-07 09:47:57.292[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 09:47:57.301[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 09:47:57.302[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 09:47:57.304[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 09:47:57.306[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 用人单位(String)
[2m2025-08-07 09:47:57.386[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:47:57.936[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:47:57.945[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:47:57.949[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:47:57.950[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:47:57.951[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xy001(String), xy001(String)
[2m2025-08-07 09:47:58.008[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:47:58.009[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 09:47:58.017[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 09:47:58.019[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 09:47:58.019[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 09:47:58.020[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 760760c056ae97081e9a7456a7ef19ba(String)
[2m2025-08-07 09:47:58.086[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-07 09:47:58.613[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 09:47:58.623[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 09:47:58.625[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 09:47:58.625[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 09:47:58.625[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-08-07 09:47:58.688[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:47:58.777[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.ID,t.XGH,t.JOB_ID,t.XNXQ,t.SQSJ,t.sqly,t.TCYS,t.SFFCAP,t.SPZT,t.YGZT,t.YGKSRQ,t.WORKFLOW_ID,t.TJZT,t.ORIGINAL_JOB_ID,t.TJSQSJ,t.TJQRSJ,t.XSQRSJ,t.ROLE_ID,t.XXMC,t.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO   FROM SYT_QGZX_STUDENT_APPLY  t    LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH)     WHERE   (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 09:47:58.805[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.ID,t.XGH,t.JOB_ID,t.XNXQ,t.SQSJ,t.sqly,t.TCYS,t.SFFCAP,t.SPZT,t.YGZT,t.YGKSRQ,t.WORKFLOW_ID,t.TJZT,t.ORIGINAL_JOB_ID,t.TJSQSJ,t.TJQRSJ,t.XSQRSJ,t.ROLE_ID,t.XXMC,t.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO   FROM SYT_QGZX_STUDENT_APPLY  t    LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH)     WHERE   (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 09:47:58.807[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.ID, t.XGH, t.JOB_ID, t.XNXQ, t.SQSJ, t.sqly, t.TCYS, t.SFFCAP, t.SPZT, t.YGZT, t.YGKSRQ, t.WORKFLOW_ID, t.TJZT, t.ORIGINAL_JOB_ID, t.TJSQSJ, t.TJQRSJ, t.XSQRSJ, t.ROLE_ID, t.XXMC, t.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO FROM SYT_QGZX_STUDENT_APPLY t LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH) WHERE (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 09:47:58.810[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m ==>  Preparing: SELECT t.ID, t.XGH, t.JOB_ID, t.XNXQ, t.SQSJ, t.sqly, t.TCYS, t.SFFCAP, t.SPZT, t.YGZT, t.YGKSRQ, t.WORKFLOW_ID, t.TJZT, t.ORIGINAL_JOB_ID, t.TJSQSJ, t.TJQRSJ, t.XSQRSJ, t.ROLE_ID, t.XXMC, t.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO FROM SYT_QGZX_STUDENT_APPLY t LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH) WHERE (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 09:47:58.811[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String), YGJS(String)
[2m2025-08-07 09:47:58.925[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:49:08.258[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 09:49:08.280[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 15 ms. Found 0 JPA repository interfaces.
[2m2025-08-07 09:49:08.281[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 09:49:08.286[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-07 09:49:08.418[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 09:49:08.426[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 09:49:08.429[0;39m [33m WARN[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.sanythadmin.**.mapper]' package. Please check your configuration.
[2m2025-08-07 09:49:08.883[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 09:49:08.891[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 09:49:08.894[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 09:49:08.987[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 09:49:08.988[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 用人单位(String)
[2m2025-08-07 09:49:09.080[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:49:09.411[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:49:09.418[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:49:09.420[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:49:09.421[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 09:49:09.422[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xy001(String), xy001(String)
[2m2025-08-07 09:49:09.510[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:49:09.513[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 09:49:09.520[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 09:49:09.522[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 09:49:09.523[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 09:49:09.523[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 760760c056ae97081e9a7456a7ef19ba(String)
[2m2025-08-07 09:49:09.587[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-07 09:49:10.215[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 09:49:10.227[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 09:49:10.229[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 09:49:10.230[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 09:49:10.230[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-08-07 09:49:10.294[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 09:49:10.306[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.ID,t.XGH,t.JOB_ID,t.XNXQ,t.SQSJ,t.sqly,t.TCYS,t.SFFCAP,t.SPZT,t.YGZT,t.YGKSRQ,t.WORKFLOW_ID,t.TJZT,t.ORIGINAL_JOB_ID,t.TJSQSJ,t.TJQRSJ,t.XSQRSJ,t.ROLE_ID,t.XXMC,t.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO   FROM SYT_QGZX_STUDENT_APPLY  t    LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH)     WHERE   (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 09:49:10.337[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.ID,t.XGH,t.JOB_ID,t.XNXQ,t.SQSJ,t.sqly,t.TCYS,t.SFFCAP,t.SPZT,t.YGZT,t.YGKSRQ,t.WORKFLOW_ID,t.TJZT,t.ORIGINAL_JOB_ID,t.TJSQSJ,t.TJQRSJ,t.XSQRSJ,t.ROLE_ID,t.XXMC,t.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO   FROM SYT_QGZX_STUDENT_APPLY  t    LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH)     WHERE   (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 09:49:10.339[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.ID, t.XGH, t.JOB_ID, t.XNXQ, t.SQSJ, t.sqly, t.TCYS, t.SFFCAP, t.SPZT, t.YGZT, t.YGKSRQ, t.WORKFLOW_ID, t.TJZT, t.ORIGINAL_JOB_ID, t.TJSQSJ, t.TJQRSJ, t.XSQRSJ, t.ROLE_ID, t.XXMC, t.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO FROM SYT_QGZX_STUDENT_APPLY t LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH) WHERE (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 09:49:10.340[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m ==>  Preparing: SELECT t.ID, t.XGH, t.JOB_ID, t.XNXQ, t.SQSJ, t.sqly, t.TCYS, t.SFFCAP, t.SPZT, t.YGZT, t.YGKSRQ, t.WORKFLOW_ID, t.TJZT, t.ORIGINAL_JOB_ID, t.TJSQSJ, t.TJQRSJ, t.XSQRSJ, t.ROLE_ID, t.XXMC, t.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO FROM SYT_QGZX_STUDENT_APPLY t LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH) WHERE (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 09:49:10.341[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String), YGJS(String)
[2m2025-08-07 09:49:10.433[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 10:02:50.638[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-07 10:02:50.660[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
[2m2025-08-07 10:02:50.661[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-07 10:02:50.665[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 2 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-07 10:02:50.753[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-07 10:02:50.758[0;39m [32m INFO[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
[2m2025-08-07 10:02:50.759[0;39m [33m WARN[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.sanythadmin.**.mapper]' package. Please check your configuration.
[2m2025-08-07 10:02:50.918[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 10:02:50.926[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 10:02:50.929[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 10:02:50.998[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 10:02:51.002[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 用人单位(String)
[2m2025-08-07 10:02:51.077[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 10:02:51.600[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 10:02:51.607[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 10:02:51.610[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 10:02:51.613[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 10:02:51.614[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xy001(String), xy001(String)
[2m2025-08-07 10:02:51.694[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 10:02:51.694[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 10:02:51.700[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 10:02:51.703[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 10:02:51.703[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 10:02:51.703[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 760760c056ae97081e9a7456a7ef19ba(String)
[2m2025-08-07 10:02:51.778[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-07 10:02:52.426[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 10:02:52.438[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 10:02:52.440[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 10:02:52.441[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 10:02:52.441[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-08-07 10:02:52.520[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 10:02:52.527[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.ID,t.XGH,t.JOB_ID,t.XNXQ,t.SQSJ,t.sqly,t.TCYS,t.SFFCAP,t.SPZT,t.YGZT,t.YGKSRQ,t.WORKFLOW_ID,t.TJZT,t.ORIGINAL_JOB_ID,t.TJSQSJ,t.TJQRSJ,t.XSQRSJ,t.ROLE_ID,t.XXMC,t.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO   FROM SYT_QGZX_STUDENT_APPLY  t    LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH)     WHERE   (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 10:02:52.563[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.ID,t.XGH,t.JOB_ID,t.XNXQ,t.SQSJ,t.sqly,t.TCYS,t.SFFCAP,t.SPZT,t.YGZT,t.YGKSRQ,t.WORKFLOW_ID,t.TJZT,t.ORIGINAL_JOB_ID,t.TJSQSJ,t.TJQRSJ,t.XSQRSJ,t.ROLE_ID,t.XXMC,t.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO   FROM SYT_QGZX_STUDENT_APPLY  t    LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH)     WHERE   (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 10:02:52.566[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.ID, t.XGH, t.JOB_ID, t.XNXQ, t.SQSJ, t.sqly, t.TCYS, t.SFFCAP, t.SPZT, t.YGZT, t.YGKSRQ, t.WORKFLOW_ID, t.TJZT, t.ORIGINAL_JOB_ID, t.TJSQSJ, t.TJQRSJ, t.XSQRSJ, t.ROLE_ID, t.XXMC, t.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO FROM SYT_QGZX_STUDENT_APPLY t LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH) WHERE (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 10:02:52.572[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m ==>  Preparing: SELECT t.ID, t.XGH, t.JOB_ID, t.XNXQ, t.SQSJ, t.sqly, t.TCYS, t.SFFCAP, t.SPZT, t.YGZT, t.YGKSRQ, t.WORKFLOW_ID, t.TJZT, t.ORIGINAL_JOB_ID, t.TJSQSJ, t.TJQRSJ, t.XSQRSJ, t.ROLE_ID, t.XXMC, t.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO FROM SYT_QGZX_STUDENT_APPLY t LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH) WHERE (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 10:02:52.575[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String), YGJS(String)
[2m2025-08-07 10:02:52.666[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 10:04:47.371[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 10:04:47.387[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-07 10:04:47.388[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 10:04:47.466[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-07 10:04:47.467[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 用人单位(String)
[2m2025-08-07 10:04:47.535[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 10:04:47.857[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 10:04:47.864[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 10:04:47.866[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 10:04:47.867[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-07 10:04:47.867[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xy001(String), xy001(String)
[2m2025-08-07 10:04:47.940[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 10:04:47.942[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 10:04:47.949[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-07 10:04:47.951[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 10:04:47.952[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-07 10:04:47.952[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 760760c056ae97081e9a7456a7ef19ba(String)
[2m2025-08-07 10:04:48.030[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-07 10:04:48.605[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 10:04:48.617[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,EID,XNXQ,JOB_CODE,JOB_NAME,JOB_TYPE_ID,START_DATE,END_DATE,START_TIME,END_TIME,WORK_HOUS,HOURLY_RATE,BZ,XQMC,LXFS,PUBLISHED,SPZT,CREATE_TIME,YGRS,LQRS,SFXWG,WORK_DAYS,SFXZSKSJ,SFMS,SFQD,YGJSSFSH,BCSFSH,DKFW,MSDD,GWZZ,GWYQ,ZPTJ,YZGBC,WORKFLOW_ID,XGH,ROLE_ID,XXMC,SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID=?
[2m2025-08-07 10:04:48.619[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 10:04:48.619[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, EID, XNXQ, JOB_CODE, JOB_NAME, JOB_TYPE_ID, START_DATE, END_DATE, START_TIME, END_TIME, WORK_HOUS, HOURLY_RATE, BZ, XQMC, LXFS, PUBLISHED, SPZT, CREATE_TIME, YGRS, LQRS, SFXWG, WORK_DAYS, SFXZSKSJ, SFMS, SFQD, YGJSSFSH, BCSFSH, DKFW, MSDD, GWZZ, GWYQ, ZPTJ, YZGBC, WORKFLOW_ID, XGH, ROLE_ID, XXMC, SFTS FROM SYT_QGZX_JOB_APPLICATION WHERE ID = ?
[2m2025-08-07 10:04:48.619[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String)
[2m2025-08-07 10:04:48.698[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-07 10:04:48.711[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.ID,t.XGH,t.JOB_ID,t.XNXQ,t.SQSJ,t.sqly,t.TCYS,t.SFFCAP,t.SPZT,t.YGZT,t.YGKSRQ,t.WORKFLOW_ID,t.TJZT,t.ORIGINAL_JOB_ID,t.TJSQSJ,t.TJQRSJ,t.XSQRSJ,t.ROLE_ID,t.XXMC,t.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO   FROM SYT_QGZX_STUDENT_APPLY  t    LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH)     WHERE   (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 10:04:48.748[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.ID,t.XGH,t.JOB_ID,t.XNXQ,t.SQSJ,t.sqly,t.TCYS,t.SFFCAP,t.SPZT,t.YGZT,t.YGKSRQ,t.WORKFLOW_ID,t.TJZT,t.ORIGINAL_JOB_ID,t.TJSQSJ,t.TJQRSJ,t.XSQRSJ,t.ROLE_ID,t.XXMC,t.SFTS,u.XGH AS joina_XGH,u.BZ1,u.BZ2,u.BZ3,u.BZ4,u.BZ5,u.BZ6,u.BZ7,u.BZ8,u.BZ9,u.BZ10,u.BZ11,u.BZ12,u.BZ13,u.BZ14,u.BZ15,u.BZ16,u.BZ17,u.BZ18,u.BZ19,u.BZ20,u.BZ21,u.BZ22,u.BZ23,u.BZ24,u.BZ25,u.BZ26,u.BZ27,u.BZ28,u.BZ29,u.BZ30,u.BZ31,u.BZ32,u.BZ33,u.BZ34,u.BZ35,u.BZ36,u.BZ37,u.BZ38,u.BZ39,u.BZ40,u.BZ41,u.BZ42,u.BZ43,u.BZ44,u.BZ45,u.BZ46,u.BZ47,u.BZ48,u.BZ49,u.BZ50,u.BZ51,u.BZ52,u.BZ53,u.BZ54,u.BZ55,u.BZ56,u.BZ57,u.BZ58,u.BZ59,u.BZ60,u.BZ61,u.BZ62,u.BZ63,u.BZ64,u.BZ65,u.BZ66,u.BZ67,u.BZ68,u.BZ69,u.BZ70,u.BZ71,u.BZ72,u.BZ73,u.BZ74,u.BZ75,u.BZ76,u.BZ77,u.BZ78,u.BZ79,u.BZ80,u.BZ81,u.BZ82,u.BZ83,u.BZ84,u.BZ85,u.BZ86,u.BZ87,u.BZ88,u.BZ89,u.BZ90,u.BZ91,u.BZ92,u.BZ93,u.BZ94,u.BZ95,u.BZ96,u.BZ97,u.BZ98,u.BZ99,u.BZ100,u.XM,u.XB,u.SJH,u.CSRQ,u.JG,u.MZMC,u.ZZMMMC,u.XQMC,u.ZJHM,u.ZJLX,u.XSLB,u.XZLX,u.RYZTID,u.USER_TYPE,u.PHOTO   FROM SYT_QGZX_STUDENT_APPLY  t    LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH)     WHERE   (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 10:04:48.750[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.ID, t.XGH, t.JOB_ID, t.XNXQ, t.SQSJ, t.sqly, t.TCYS, t.SFFCAP, t.SPZT, t.YGZT, t.YGKSRQ, t.WORKFLOW_ID, t.TJZT, t.ORIGINAL_JOB_ID, t.TJSQSJ, t.TJQRSJ, t.XSQRSJ, t.ROLE_ID, t.XXMC, t.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO FROM SYT_QGZX_STUDENT_APPLY t LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH) WHERE (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 10:04:48.751[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m ==>  Preparing: SELECT t.ID, t.XGH, t.JOB_ID, t.XNXQ, t.SQSJ, t.sqly, t.TCYS, t.SFFCAP, t.SPZT, t.YGZT, t.YGKSRQ, t.WORKFLOW_ID, t.TJZT, t.ORIGINAL_JOB_ID, t.TJSQSJ, t.TJQRSJ, t.XSQRSJ, t.ROLE_ID, t.XXMC, t.SFTS, u.XGH AS joina_XGH, u.BZ1, u.BZ2, u.BZ3, u.BZ4, u.BZ5, u.BZ6, u.BZ7, u.BZ8, u.BZ9, u.BZ10, u.BZ11, u.BZ12, u.BZ13, u.BZ14, u.BZ15, u.BZ16, u.BZ17, u.BZ18, u.BZ19, u.BZ20, u.BZ21, u.BZ22, u.BZ23, u.BZ24, u.BZ25, u.BZ26, u.BZ27, u.BZ28, u.BZ29, u.BZ30, u.BZ31, u.BZ32, u.BZ33, u.BZ34, u.BZ35, u.BZ36, u.BZ37, u.BZ38, u.BZ39, u.BZ40, u.BZ41, u.BZ42, u.BZ43, u.BZ44, u.BZ45, u.BZ46, u.BZ47, u.BZ48, u.BZ49, u.BZ50, u.BZ51, u.BZ52, u.BZ53, u.BZ54, u.BZ55, u.BZ56, u.BZ57, u.BZ58, u.BZ59, u.BZ60, u.BZ61, u.BZ62, u.BZ63, u.BZ64, u.BZ65, u.BZ66, u.BZ67, u.BZ68, u.BZ69, u.BZ70, u.BZ71, u.BZ72, u.BZ73, u.BZ74, u.BZ75, u.BZ76, u.BZ77, u.BZ78, u.BZ79, u.BZ80, u.BZ81, u.BZ82, u.BZ83, u.BZ84, u.BZ85, u.BZ86, u.BZ87, u.BZ88, u.BZ89, u.BZ90, u.BZ91, u.BZ92, u.BZ93, u.BZ94, u.BZ95, u.BZ96, u.BZ97, u.BZ98, u.BZ99, u.BZ100, u.XM, u.XB, u.SJH, u.CSRQ, u.JG, u.MZMC, u.ZZMMMC, u.XQMC, u.ZJHM, u.ZJLX, u.XSLB, u.XZLX, u.RYZTID, u.USER_TYPE, u.PHOTO FROM SYT_QGZX_STUDENT_APPLY t LEFT JOIN SYT_USER_INFO u ON (u.XGH = t.XGH) WHERE (t.JOB_ID = ? AND t.YGZT = ?) ORDER BY t.SQSJ DESC
[2m2025-08-07 10:04:48.752[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m ==> Parameters: 84be3e3789866b57e024851de83f7e62(String), YGJS(String)
[2m2025-08-07 10:04:48.835[0;39m [32mDEBUG[0;39m [35m25584[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectJoinList              [0;39m [2m:[0;39m <==      Total: 1
